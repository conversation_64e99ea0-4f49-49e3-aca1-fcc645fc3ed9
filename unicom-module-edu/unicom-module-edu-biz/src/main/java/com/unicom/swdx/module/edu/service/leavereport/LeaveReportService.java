package com.unicom.swdx.module.edu.service.leavereport;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassInfoRespVO;
import com.unicom.swdx.module.edu.controller.admin.leavereport.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.leavereport.LeaveReportDO;

import java.util.List;

public interface LeaveReportService {
    Long createLeaveReport(LeaveReportCreateReqVO createReqVO);

    TeacherLeaveReportLeaveVO getLeaveReportDetail(Long leaveId, Integer fillStatus);

    List<LeaveReportRespVO> getLeaveReportList(Long classId);

    Boolean remind(LeaveReportRemindReqVO remindReqVO);

    LeaveReportTimeVO getLeaveReportTime(Long leaveId);

    /**
     * 获取所有离校申请
     *
     * @return 离校申请列表
     */
    List<LeaveReportRespVO> getAllLeaveReportList();

    /**
     * 获取离校申请列表（支持筛选）
     *
     * @param queryReqVO 查询条件
     * @return 离校申请列表
     */
    List<LeaveReportRespVO> getLeaveReportList(LeaveReportQueryReqVO queryReqVO);

    /**
     * 获取离校申请分页列表（支持筛选）
     *
     * @param queryReqVO 查询条件
     * @return 离校申请分页列表
     */
    PageResult<LeaveReportRespVO> getLeaveReportPage(LeaveReportQueryReqVO queryReqVO);

    /**
     * 删除离校申请
     *
     * @param id 编号
     */
    void deleteLeaveReport(Long id);

    /**
     * 获取当前租户下所有开班中的班级
     *
     * @return 开班中的班级列表
     */
    List<ClassInfoRespVO> getOpeningClasses();

    /**
     * 获取当前租户下所有班级
     *
     * @param className 班级名称（可选，用于过滤）
     * @return 班级列表
     */
    List<ClassInfoRespVO> getAllClassesWithFilter(String className);


    /**
     * 获取导出所有离校报备 Excel 数据，只支持按ID过滤
     *
     * @param ids 指定要导出的ID列表（可选）
     * @return 导出数据列表
     */
    List<LeaveReportExcelVO> getExportAllLeaveReportExcel(List<Long> ids);
}
