package com.unicom.swdx.module.edu.service.clockininfo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.framework.tenant.core.context.TenantContextHolder;
import com.unicom.swdx.module.edu.controller.admin.classcourse.vo.ClassCourseRespVO;
import com.unicom.swdx.module.edu.controller.admin.classcourse.vo.MyClassScheduleParamsVO;
import com.unicom.swdx.module.edu.controller.admin.classcourse.vo.MyClassScheduleVO;
import com.unicom.swdx.module.edu.controller.admin.classcourse.vo.ScheduleChartVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassRuleClockingInVO;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.dto.attendancerate.TraineeAttendanceInfoDTO;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.*;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate.*;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.businesscenter.attendancerate.AttendanceDetailsVO;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.businesscenter.attendancerate.AttendanceRateForBusinessCenterReqVO;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.businesscenter.attendancerate.AttendanceRateForBusinessCenterRespVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo.EvaluationDistributeVO;
import com.unicom.swdx.module.edu.controller.admin.ruletemplate.vo.RuleTemplateRespVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.AppTraineeGroupRespVO;
import com.unicom.swdx.module.edu.convert.clockininfo.ClockInInfoConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classclockcalendar.ClassClockCalendarDO;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.classroomlibrary.ClassroomLibraryDO;
import com.unicom.swdx.module.edu.dal.dataobject.clockininfo.ClockInInfoDO;
import com.unicom.swdx.module.edu.dal.dataobject.courses.CoursesDO;
import com.unicom.swdx.module.edu.dal.dataobject.plan.PlanDO;
import com.unicom.swdx.module.edu.dal.dataobject.rollcallrecord.RollcallRecordDO;
import com.unicom.swdx.module.edu.dal.dataobject.rollcallsignin.RollcallSignInDO;
import com.unicom.swdx.module.edu.dal.dataobject.ruletemplate.RuleTemplateDO;
import com.unicom.swdx.module.edu.dal.dataobject.ruletemplate.RuleTemplateLocationDO;
import com.unicom.swdx.module.edu.dal.dataobject.schoolaccommodationattendance.SchoolAccommodationAttendanceDO;
import com.unicom.swdx.module.edu.dal.dataobject.traineeleave.TraineeLeaveDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classclockcalendar.ClassClockCalendarMapper;
import com.unicom.swdx.module.edu.dal.mysql.classcourse.ClassCourseMapper;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.clockininfo.ClockInInfoMapper;
import com.unicom.swdx.module.edu.dal.mysql.courses.CoursesMapper;
import com.unicom.swdx.module.edu.dal.mysql.rollcallsignin.RollcallSignInMapper;
import com.unicom.swdx.module.edu.dal.mysql.ruletemplate.RuleTemplateMapper;
import com.unicom.swdx.module.edu.dal.mysql.schoolaccommodationattendance.SchoolAccommodationAttendanceMapper;
import com.unicom.swdx.module.edu.dal.mysql.traineeleave.TraineeLeaveMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.dal.redis.edu.TraineeLoginTypeRedisDAO;
import com.unicom.swdx.module.edu.enums.attendance.AttendanceStatusEnum;
import com.unicom.swdx.module.edu.enums.attendance.AttendanceTypeEnum;
import com.unicom.swdx.module.edu.enums.attendance.UpdateAttendanceStatusEnum;
import com.unicom.swdx.module.edu.enums.classcourse.PeriodEnum;
import com.unicom.swdx.module.edu.enums.classcourse.StatusEnum;
import com.unicom.swdx.module.edu.enums.classmanagement.IsCheckElseEnum;
import com.unicom.swdx.module.edu.enums.classmanagement.IsCheckEnum;
import com.unicom.swdx.module.edu.enums.clockin.MealSleepPeriodEnum;
import com.unicom.swdx.module.edu.enums.clockininfo.ClockRateRuleEnum;
import com.unicom.swdx.module.edu.enums.clockininfo.ClockStatusEnum;
import com.unicom.swdx.module.edu.enums.clockininfo.MealPeriodEnum;
import com.unicom.swdx.module.edu.enums.clockininfo.TypeEnum;
import com.unicom.swdx.module.edu.enums.electiverelease.ClassDayPeriodEnum;
import com.unicom.swdx.module.edu.enums.plan.PlanStatusEnum;
import com.unicom.swdx.module.edu.enums.ratio.RatioValValueEnum;
import com.unicom.swdx.module.edu.enums.rollcallsignin.SignInTypeEnum;
import com.unicom.swdx.module.edu.enums.trainee.TraineeStatusEnum;
import com.unicom.swdx.module.edu.enums.traineeleave.TraineeLeaveStatus;
import com.unicom.swdx.module.edu.enums.traineeleave.TraineeLeaveType;
import com.unicom.swdx.module.edu.service.classcourse.ClassCourseServiceImpl;
import com.unicom.swdx.module.edu.service.classmanagement.ClassManagementServiceImpl;
import com.unicom.swdx.module.edu.service.classroomlibrary.ClassroomLibraryServiceImpl;
import com.unicom.swdx.module.edu.service.evaluationdetail.EvaluationDetailService;
import com.unicom.swdx.module.edu.service.plan.PlanServiceImpl;
import com.unicom.swdx.module.edu.service.rollcallrecord.RollcallRecordServiceImpl;
import com.unicom.swdx.module.edu.service.ruletemplate.RuleTemplateServiceImpl;
import com.unicom.swdx.module.edu.service.training.TraineeServiceImpl;
import com.unicom.swdx.module.edu.utils.numberconverter.NumberConverter;
import com.unicom.swdx.module.system.api.dict.DictDataApi;
import com.unicom.swdx.module.system.api.dict.dto.DictDataRespDTO;
import com.unicom.swdx.module.system.api.tenant.TenantApi;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 考勤签到 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ClockInInfoServiceImpl extends ServiceImpl<ClockInInfoMapper, ClockInInfoDO> implements ClockInInfoService {

    @Resource
    private ClockInInfoMapper clockInInfoMapper;

    @Resource
    PlanServiceImpl planService;

    @Resource
    ClassCourseServiceImpl classCourseService;

    @Resource
    private EvaluationDetailService evaluationDetailService;

    @Resource
    TraineeServiceImpl traineeService;

    @Resource
    ClassManagementServiceImpl classManagementService;

    @Resource
    ClassManagementMapper classManagementMapper;

    @Resource
    SchoolAccommodationAttendanceMapper schoolAccommodationAttendanceMapper;

    @Resource
    private TraineeMapper traineeMapper;

    @Resource
    ClassCourseMapper classCourseMapper;

    @Resource
    RuleTemplateServiceImpl ruleTemplateService;

    @Resource
    RuleTemplateMapper ruleTemplateMapper;

    @Resource
    RollcallSignInMapper rollcallSignInMapper;

    @Resource
    ClassroomLibraryServiceImpl classroomLibraryService;

    @Resource
    CoursesMapper coursesMapper;

    @Resource
    RollcallRecordServiceImpl rollcallRecordService;

    @Resource
    TraineeLeaveMapper traineeLeaveMapper;

    @Resource
    ClassClockCalendarMapper classClockCalendarMapper;

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private TraineeLoginTypeRedisDAO traineeLoginTypeRedisDAO;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private TenantApi tenantApi;

    // 考勤率字典类型
    @Value("${dict.type.attendance.rate-rule:edu_tenant_attendance_rate_rule}")
    private String rateRuleDictType;

    // 默认考勤规则字典值
    @Value("${dict.type.attendance.default-rule-type:1}")
    private Integer defaultRuleType;

    private final static String defaultRuleDictLabel = "default";

    // 刷新考勤签到表分布式锁key
    private static final String GENREATE_RECORDS_LOCK_KEY = "generateRecordsLock";

    // 锁自动过期时间 秒
    @Value("${generateRecords.lockExpireTime:5}")
    private Long generateRecordsLockExpireTime;

    private static final String ZERO_RATE_STRING = "0.00";

    private static final Integer ZERO_INT = 0;

    private static final String UN_GROUPED_NAME = "未分组";

    @Override
    public Integer createClockInInfo(ClockInInfoCreateReqVO createReqVO) {
        // 插入
        ClockInInfoDO clockInInfo = ClockInInfoConvert.INSTANCE.convert(createReqVO);
        clockInInfoMapper.insert(clockInInfo);
        // 返回
        return clockInInfo.getId();
    }

    @Override
    public void updateClockInInfo(ClockInInfoUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateClockInInfoExists(updateReqVO.getId());
        // 更新
        ClockInInfoDO updateObj = ClockInInfoConvert.INSTANCE.convert(updateReqVO);
        clockInInfoMapper.updateById(updateObj);
    }

    @Override
    public void deleteClockInInfo(Integer id) {
        // 校验存在
        this.validateClockInInfoExists(id);
        // 删除
        clockInInfoMapper.deleteById(id);
    }

    private ClockInInfoDO validateClockInInfoExists(Serializable id) {
        ClockInInfoDO clockInInfoDO = clockInInfoMapper.selectById(id);
        if (Objects.isNull(clockInInfoDO)) {
            throw exception(CLOCK_IN_INFO_NOT_EXISTS);
        }
        clockInInfoDO.setUpdateTime(null);
        clockInInfoDO.setUpdater(null);
        return clockInInfoDO;
    }

    private RollcallRecordDO validateRollcallRecordExists(Serializable id) {
        RollcallRecordDO rollcallRecordDO = rollcallRecordService.getById(id);
        if (Objects.isNull(rollcallRecordDO)) {
            throw exception(CLOCK_IN_INFO_NOT_EXISTS);
        }
        rollcallRecordDO.setUpdateTime(null);
        rollcallRecordDO.setUpdater(null);
        return rollcallRecordDO;
    }

    @Override
    public ClockInInfoDO getClockInInfo(Integer id) {
        return clockInInfoMapper.selectById(id);
    }

    @Override
    public List<ClockInInfoDO> getClockInInfoList(Collection<Integer> ids) {
        return clockInInfoMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ClockInInfoReturnVO> getClockInInfoPage(HttpServletRequest request, ClockInInfoPageReqVO pageReqVO) {

        Integer ruleType = getRuleType();

        // 判断该账户是否是学员
        Long userIds = SecurityFrameworkUtils.getLoginUserId();

        if (!traineeService.selectTraineeByUserId(request,userIds)) {
            throw exception(NO_PERMISSION_ERROR);
        }

        // 替换掉特殊符号  %  _
        if (StringUtils.isNotBlank(pageReqVO.getName())) {
            pageReqVO.setName(pageReqVO.getName().replaceAll("([%_])", "\\\\$1"));
        }


        Page buildPage = MyBatisUtils.buildPage(pageReqVO);

        PageResult<ClockInInfoReturnVO> pageList = null;


        if (pageReqVO.getClockInType() == 0) {

            // 到课详情
            List<ClockInInfoReturnVO> attendanceCheck = clockInInfoMapper.selectPageClockInList(buildPage, pageReqVO);
            // 插入比率
            for (ClockInInfoReturnVO vo : attendanceCheck) {

                double traineeCount = vo.getTraineeCount();
                double vacateCount = vo.getVacateCount();
                double attendanceCount = vo.getAttendanceCount();
                double denominator;

                // 考勤率计算规则
                if (ClockRateRuleEnum.HAS_LEAVE.getCode().equals(ruleType)){
                    denominator = traineeCount - vacateCount;
                }else {
                    denominator = traineeCount;
                }

                double ratio = 0.0;

                if (denominator != 0) {
                    ratio = attendanceCount / denominator;
                }

                // 保留两位小数
                double ratioRounded = Math.round(ratio * 100.0) / 100.0;

                if (ratioRounded > RatioValValueEnum.MAX_DECIMAL.getValue()){
                    ratioRounded = RatioValValueEnum.MAX_DECIMAL.getValue();
                }
                vo.setRatio(ratioRounded);

            }


            pageList = new PageResult<>(attendanceCheck, buildPage.getTotal());

        } else if (pageReqVO.getClockInType() == 1) {

            // 就餐详情
            List<ClockInInfoReturnVO> mealAttendance = clockInInfoMapper.selectPageListMealAttendance(buildPage, pageReqVO);

            // 插入比率
            for (ClockInInfoReturnVO vo : mealAttendance) {

                double traineeCount = vo.getTraineeCount();
                double vacateCount = vo.getVacateCount();
                double attendanceCount = vo.getAttendanceCount();

                double denominator;

                // 考勤率计算规则
                if (ClockRateRuleEnum.HAS_LEAVE.getCode().equals(ruleType)){
                    denominator = traineeCount - vacateCount;
                }else {
                    denominator = traineeCount;
                }

                double ratio = 0.0;

                if (denominator != 0) {
                    ratio = attendanceCount / denominator;
                }

                // 保留两位小数
                double ratioRounded = Math.round(ratio * 100.0) / 100.0;
                if (ratioRounded > RatioValValueEnum.MAX_DECIMAL.getValue()){
                    ratioRounded = RatioValValueEnum.MAX_DECIMAL.getValue();
                }
                vo.setRatio(ratioRounded);

            }

            pageList = new PageResult<>(mealAttendance, buildPage.getTotal());

        } else {

            // 住宿详情
            List<ClockInInfoReturnVO> checkIn = clockInInfoMapper.selectPageListCheckIn(buildPage, pageReqVO);

            // 插入比率
            for (ClockInInfoReturnVO vo : checkIn) {

                double traineeCount = vo.getTraineeCount();
                double vacateCount = vo.getVacateCount();
                double attendanceCount = vo.getAttendanceCount();

                double denominator;

                // 考勤率计算规则
                if (ClockRateRuleEnum.HAS_LEAVE.getCode().equals(ruleType)){
                    denominator = traineeCount - vacateCount;
                }else {
                    denominator = traineeCount;
                }

                double ratio = 0.0;

                if (denominator != 0) {
                    ratio = attendanceCount / denominator;
                }

                // 保留两位小数
                double ratioRounded = Math.round(ratio * 100.0) / 100.0;
                if (ratioRounded > RatioValValueEnum.MAX_DECIMAL.getValue()){
                    ratioRounded = RatioValValueEnum.MAX_DECIMAL.getValue();
                }
                vo.setRatio(ratioRounded);

            }

            pageList = new PageResult<>(checkIn, buildPage.getTotal());
        }


        return pageList;
    }

    @Override
    public MyClockInInfoRespVO getMyClockInInfoList(MyClockInInfoReqVO reqVO) {

        MyClassScheduleParamsVO myClassScheduleParamsVO = new MyClassScheduleParamsVO();
        myClassScheduleParamsVO.setStartTime(reqVO.getDate() + " 00:00:00");
        myClassScheduleParamsVO.setEndTime(reqVO.getDate() + " 23:59:59");

        // 获取学员课表
        List<ScheduleChartVO> myClassSchedule = classCourseService.getMyClassSchedule(myClassScheduleParamsVO);

        // 根据 class_course_id 是否为空，分为两个集合
        List<CourseVO> classCourseList = new ArrayList<>();


        Long userId = SecurityFrameworkUtils.getLoginUserId();
        // 根据userId获取对应的学员id
        TraineeDO traineeDO = traineeService.getTraineeByUserId(userId);

        if (traineeDO == null) {
            throw exception(NO_PERMISSION_ERROR);
        }
        if (reqVO.getTraineeId() == null){
            reqVO.setTraineeId(traineeDO.getId());
        }

        List<CourseVO> clockInfoDOList = clockInInfoMapper.selectListByTraineeId(reqVO);
        List<CourseVO> clockInfoDOList1 = clockInInfoMapper.selectListByTraineeId1(reqVO);

        // 根据 class_course_id 是否为空，分为两个集合
        Map<Long, CourseVO> courseVOMap = clockInfoDOList1.stream()
                .filter(info -> info.getClassCourseId() != null)
                .collect(Collectors.toMap(CourseVO::getClassCourseId, Function.identity(), (k1, k2) -> k1));

        if (CollUtil.isNotEmpty(myClassSchedule)) {

            List<MyClassScheduleVO> schedules = myClassSchedule.get(0).getSchedules();

            for (MyClassScheduleVO schedule : schedules) {
                CourseVO courseVO = new CourseVO();

                courseVO.setName(schedule.getCourseName());
                courseVO.setClassCourseId(schedule.getClassCourseId());
                courseVO.setPeriod(Integer.valueOf(schedule.getPeriod()));
                courseVO.setBeginTime(schedule.getBeginTime());
                courseVO.setIsCheck(schedule.getIsCheck());

                CourseVO vo = courseVOMap.get(schedule.getClassCourseId());

                if (vo != null) {
                    courseVO.setType(0);
                    if (Objects.equals(vo.getTraineeStatus(), "0")) {
                        courseVO.setTraineeStatus("未签");
                    } else if (Objects.equals(vo.getTraineeStatus(), "1")) {
                        courseVO.setTraineeStatus("正常");
                    } else if (Objects.equals(vo.getTraineeStatus(), "2")) {
                        courseVO.setTraineeStatus("迟到");
                    } else if (Objects.equals(vo.getTraineeStatus(), "3")) {
                        courseVO.setTraineeStatus("请假");
                    }
                }

                classCourseList.add(courseVO);
            }
        }

        List<CourseVO> mealSleepList = clockInfoDOList.stream()
                .filter(info -> info.getClassCourseId() == null)
                .collect(Collectors.toList());

        for (CourseVO mealSleep : mealSleepList) {

            if (Objects.equals(mealSleep.getTraineeStatus(), "0")) {
                mealSleep.setTraineeStatus("未签");
            }

            if (Objects.equals(mealSleep.getTraineeStatus(), "1")) {
                mealSleep.setTraineeStatus("正常");
            }

            if (Objects.equals(mealSleep.getTraineeStatus(), "2")) {
                mealSleep.setTraineeStatus("正常");
            }

            if (Objects.equals(mealSleep.getTraineeStatus(), "3")) {
                mealSleep.setTraineeStatus("请假");
            }

        }

        for (CourseVO vo : mealSleepList) {
            if (Objects.isNull(vo.getMealPeriod())) {
                vo.setMealPeriod(MealSleepPeriodEnum.SLEEPING.getCode());
                vo.setName(MealSleepPeriodEnum.SLEEPING.getDesc());
            }

            if (Objects.equals(vo.getMealPeriod(), MealSleepPeriodEnum.BREAKFAST.getCode())) {
                vo.setName(MealSleepPeriodEnum.BREAKFAST.getDesc());
            } else if (Objects.equals(vo.getMealPeriod(), MealSleepPeriodEnum.LUNCH.getCode())) {
                vo.setName(MealSleepPeriodEnum.LUNCH.getDesc());
            } else if (Objects.equals(vo.getMealPeriod(), MealSleepPeriodEnum.DINNER.getCode())) {
                vo.setName(MealSleepPeriodEnum.DINNER.getDesc());
            }

        }

        Map<Integer, List<CourseVO>> courseMap = classCourseList.stream().collect(Collectors.groupingBy(CourseVO::getPeriod));

        Map<Integer, List<CourseVO>> mealSleepMap = mealSleepList.stream().collect(Collectors.groupingBy(CourseVO::getMealPeriod));


        // 早餐
        List<CourseVO> morningMealList = mealSleepMap.getOrDefault(MealSleepPeriodEnum.BREAKFAST.getCode(), new ArrayList<>());

        if (CollUtil.isEmpty(morningMealList)) {
            morningMealList.add(new CourseVO());
        }
        MyClockInInfoRespVO respVO = new MyClockInInfoRespVO();
        respVO.setBreakfast(morningMealList);
//        List<MyClockInInfoRespVO.CourseVO> myClockInInfoList = new ArrayList<>(morningMealList);

        // 上午课
        List<CourseVO> morningCourseList = courseMap.getOrDefault(PeriodEnum.MORNING.getCode(), new ArrayList<>())
                .stream().sorted(Comparator.comparing(CourseVO::getBeginTime)).collect(Collectors.toList());

        int i = 1;
        for (CourseVO courseVO : morningCourseList) {
            courseVO.setName("上午第" + NumberConverter.arabicToChinese(i) + "节：" + courseVO.getName());
            i++;
        }


//        myClockInInfoList.addAll(morningCourseList);

        respVO.setMorningCourse(morningCourseList.stream().filter(course->course.getIsCheck()!= null && course.getIsCheck()).collect(Collectors.toList()));
        // 午餐
        List<CourseVO> afternoonMealList = mealSleepMap.getOrDefault(MealSleepPeriodEnum.LUNCH.getCode(), new ArrayList<>());
//        myClockInInfoList.addAll(afternoonMealList);

        if (CollUtil.isEmpty(afternoonMealList)) {
            afternoonMealList.add(new CourseVO());
        }

        respVO.setLunch(afternoonMealList);
        // 下午课程
        List<CourseVO> afternoonCourseList = courseMap.getOrDefault(PeriodEnum.AFTERNOON.getCode(), new ArrayList<>())
                .stream().sorted(Comparator.comparing(CourseVO::getBeginTime)).collect(Collectors.toList());
//        myClockInInfoList.addAll(afternoonCourseList);

        i = 1;
        for (CourseVO courseVO : afternoonCourseList) {
            courseVO.setName("下午第" + NumberConverter.arabicToChinese(i) + "节：" + courseVO.getName());
            i++;
        }
        respVO.setAfternoonCourse(afternoonCourseList.stream().filter(course->course.getIsCheck()!= null && course.getIsCheck()).collect(Collectors.toList()));
        // 晚餐
        List<CourseVO> eveningMealList = mealSleepMap.getOrDefault(MealSleepPeriodEnum.DINNER.getCode(), new ArrayList<>());
//        myClockInInfoList.addAll(eveningMealList);

        if (CollUtil.isEmpty(eveningMealList)) {
            eveningMealList.add(new CourseVO());
        }
        respVO.setDinner(eveningMealList);

        // 晚上课程
        List<CourseVO> eveningCourseList = courseMap.getOrDefault(PeriodEnum.EVENING.getCode(), new ArrayList<>())
                .stream()
                .sorted(Comparator.comparing(CourseVO::getBeginTime))
                .collect(Collectors.toList());
//        myClockInInfoList.addAll(eveningCourseList);

        i = 1;
        for (CourseVO courseVO : eveningCourseList) {
            courseVO.setName("晚上第" + NumberConverter.arabicToChinese(i) + "节：" + courseVO.getName());
            i++;
        }
        respVO.setEveningCourse(eveningCourseList.stream().filter(course->course.getIsCheck()!= null && course.getIsCheck()).collect(Collectors.toList()));
        // 住宿
        List<CourseVO> sleepList = mealSleepMap.getOrDefault(MealSleepPeriodEnum.SLEEPING.getCode(), new ArrayList<>());

        if (CollUtil.isEmpty(sleepList)) {
            sleepList.add(new CourseVO());
        }

        respVO.setSleep(sleepList);
        return respVO;
    }

    /**
     * 生成学员每日待打卡记录
     */
    @XxlJob("RecordsJob")
    @TenantIgnore
    @Override
    public void generateRecords(){

        TenantContextHolder.setIgnore(true);

        log.info("generateRecords() TenantContextHolder.isIgnore():{}",TenantContextHolder.isIgnore());
        RLock lock = redissonClient.getLock(GENREATE_RECORDS_LOCK_KEY);
        boolean isLocked = false;
        try{
            isLocked  = lock.tryLock(0, generateRecordsLockExpireTime, TimeUnit.SECONDS);
            if (!isLocked) {
                return;
            }
            // 大课考勤记录不冲突
            List<ClockInInfoDO> clockInInfoDOLectureList = this.lambdaQuery().eq(ClockInInfoDO::getDate, LocalDate.now()).list();
            // 待插入的签到记录
            List<ClockInInfoDO> clockInInfoDOList = new ArrayList<>();
            LocalDate nowDate = LocalDate.now();
            // 开启的规则模版
            List<RuleTemplateDO> templateList = ruleTemplateService.lambdaQuery()
                    .list();
            Map<Long, RuleTemplateDO> templateMap = templateList.stream()
                    .collect(Collectors.toMap(RuleTemplateDO::getId, rtdo -> rtdo, (existing, replacement) -> existing));
            // 班级考勤规则对应信息
            List<ClassRuleClockingInVO> classClockInList = classManagementMapper.getClassClockIn();
            Map<Long, ClassRuleClockingInVO> classClockInMap = classClockInList.stream()
                    .collect(Collectors.toMap(ClassRuleClockingInVO::getClassId, vo -> vo));

            // 1、获取到课考勤信息
            getCourseClockInfo(nowDate, classClockInMap, templateMap, clockInInfoDOList);
            // 2、获取就餐、住宿考勤信息
            getDineAndLiveClockInfo(nowDate, classClockInMap, templateMap, clockInInfoDOList);
            // 3、请假及报道时间晚于考勤开始时间的数据过滤处理
            filterLate(clockInInfoDOList);
            // 4、根据当前的最新状态更新签到表，且不变动已有的且先时间应该生成的数据，根据id结对匹配去重
            insertRecords(clockInInfoDOLectureList, clockInInfoDOList);

        }catch (InterruptedException e){
            log.error(e.getMessage(),e);
            Thread.currentThread().interrupt();
        } finally {
            if (isLocked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 插入考勤记录并删除多余记录
     * 该方法首先过滤掉已经插入的 lecture（大课）考勤记录、就餐记录和住宿记录，然后批量插入剩余的考勤记录
     * 最后，该方法还会删除那些在原始列表中不存在的考勤记录，以确保数据一致性
     *
     * @param clockInInfoDOLectureList 讲座考勤信息列表，用于获取已插入的记录
     * @param clockInInfoDOList        所有考勤信息列表，用于过滤和批量插入
     */
    private void insertRecords(List<ClockInInfoDO> clockInInfoDOLectureList, List<ClockInInfoDO> clockInInfoDOList) {
        // 去除匹配大课考勤已插入记录
        Set<Pair<Long, Long>> lectureSet = clockInInfoDOLectureList.stream()
                .filter(ci -> ci.getTraineeId() != null && ci.getClassCourseId() != null)
                .map(ci -> new Pair<>(ci.getTraineeId(), ci.getClassCourseId()))
                .collect(Collectors.toSet());
        // 去除匹配今日已生成的就餐记录
        Set<Pair<Long, Integer>> dineSet = clockInInfoDOLectureList.stream()
                .filter(ci -> ci.getTraineeId() != null && ci.getMealPeriod() != null)
                .map(ci -> new Pair<>(ci.getTraineeId(), ci.getMealPeriod()))
                .collect(Collectors.toSet());
        // 去除匹配今日已生成的住宿记录
        Set<Pair<Long, Integer>> liveSet = clockInInfoDOLectureList.stream()
                .filter(ci -> ci.getTraineeId() != null && TypeEnum.LIVE.getCode().equals(ci.getType()))
                .map(ci -> new Pair<>(ci.getTraineeId(), TypeEnum.LIVE.getCode()))
                .collect(Collectors.toSet());
        List<ClockInInfoDO> filteredList = clockInInfoDOList.stream()
                .filter(ci -> !lectureSet.contains(new Pair<>(ci.getTraineeId(), ci.getClassCourseId())))
                .filter(ci -> !dineSet.contains(new Pair<>(ci.getTraineeId(), ci.getMealPeriod())))
                .filter(ci -> !liveSet.contains(new Pair<>(ci.getTraineeId(), ci.getType())))
                .collect(Collectors.toList());
        // 最后结果保留
        clockInInfoMapper.insertBatch(filteredList);
        // 旧有数据反方向比对并删除
        // 去除匹配大课考勤已插入记录
        Set<Pair<Long, Long>> lectureDeleteSet = clockInInfoDOList.stream()
                .filter(ci -> ci.getTraineeId() != null && ci.getClassCourseId() != null)
                .map(ci -> new Pair<>(ci.getTraineeId(), ci.getClassCourseId()))
                .collect(Collectors.toSet());
        // 去除匹配今日已生成的就餐记录
        Set<Pair<Long, Integer>> dineDeleteSet = clockInInfoDOList.stream()
                .filter(ci -> ci.getTraineeId() != null && ci.getMealPeriod() != null)
                .map(ci -> new Pair<>(ci.getTraineeId(), ci.getMealPeriod()))
                .collect(Collectors.toSet());
        // 去除匹配今日已生成的住宿记录
        Set<Pair<Long, Integer>> liveDeleteSet = clockInInfoDOList.stream()
                .filter(ci -> ci.getTraineeId() != null && TypeEnum.LIVE.getCode().equals(ci.getType()))
                .map(ci -> new Pair<>(ci.getTraineeId(), TypeEnum.LIVE.getCode()))
                .collect(Collectors.toSet());
        List<ClockInInfoDO> filteredDeleteList = clockInInfoDOLectureList.stream()
                .filter(ci -> !lectureDeleteSet.contains(new Pair<>(ci.getTraineeId(), ci.getClassCourseId())))
                .filter(ci -> !dineDeleteSet.contains(new Pair<>(ci.getTraineeId(), ci.getMealPeriod())))
                .filter(ci -> !liveDeleteSet.contains(new Pair<>(ci.getTraineeId(), ci.getType())))
                .collect(Collectors.toList());
        List<Integer> deleteIds = filteredDeleteList.stream().map(ClockInInfoDO::getId).collect(Collectors.toList());
        if (!deleteIds.isEmpty()) {
            clockInInfoMapper.deleteBatchIds(deleteIds);
        }
    }

    /**
     * 过滤迟到及请假的签到信息
     * 此方法主要目的是识别并标记那些因请假而应被视为请假状态而非迟到状态的签到记录
     * 它首先获取所有已批准的请假信息，然后检查每个签到记录是否与任何请假时间重叠
     * 如果签到时间与请假时间重叠，则该签到记录被标记为请假状态，并记录请假类型
     * 最后，任何报告时间晚于考勤开始时间的签到记录将被移除，不计入签到表
     *
     * @param clockInInfoDOList 签到信息列表，包含所有待处理的签到记录
     */
    private void filterLate(List<ClockInInfoDO> clockInInfoDOList) {
        // 学员请假信息
        List<TraineeLeaveDO> leaveInfoList = traineeLeaveMapper.selectList(new LambdaQueryWrapperX<TraineeLeaveDO>()
                .isNotNull(TraineeLeaveDO::getStartTime)
                .isNotNull(TraineeLeaveDO::getEndTime)
                .eq(TraineeLeaveDO::getStatus, TraineeLeaveStatus.OK.getCode()));
        if (CollUtil.isNotEmpty(leaveInfoList)) {
            for (ClockInInfoDO clockInInfoDO : clockInInfoDOList) {
                // // 1.3.0就餐住宿无请假概念
                // // 2025-02-19恢复住宿、就餐请假概念
                // if (TypeEnum.DINE.getCode().equals(clockInInfoDO.getType())
                //         || TypeEnum.LIVE.getCode().equals(clockInInfoDO.getType())) {
                //     continue;
                // }
                List<TraineeLeaveDO> traineeLeaveInfoList = leaveInfoList.stream()
                        .filter(vo -> vo.getTraineeId().equals(clockInInfoDO.getTraineeId()))
                        .collect(Collectors.toList());
                boolean isConflict = false;
                Integer leaveType = null;
                for (TraineeLeaveDO traineeLeaveDO : traineeLeaveInfoList) {
                    if (isTimeOverlap(traineeLeaveDO.getStartTime(), traineeLeaveDO.getEndTime(), clockInInfoDO.getCheckBeginTime(), clockInInfoDO.getCheckEndTime())) {
                        isConflict = true;
                        leaveType = traineeLeaveDO.getLeaveType();
                        break;
                    }
                }
                if (isConflict) {
                    clockInInfoDO.setTraineeStatus(ClockStatusEnum.LEAVE.getCode());
                    clockInInfoDO.setLeaveType(leaveType);
                }
            }
        }
        // 报到时间晚于考勤开始时间则不记入签到表
        clockInInfoDOList.removeIf(clockInInfoDO ->
                clockInInfoDO.getReportTime() != null &&
                        clockInInfoDO.getCheckBeginTime() != null &&
                        clockInInfoDO.getReportTime().isAfter(clockInInfoDO.getCheckBeginTime()));
    }

    /**
     * 获取学员的就餐和住宿考勤信息
     *
     * @param nowDate 当前日期，用于查询考勤信息
     * @param classClockInMap 班级考勤映射，用于获取每个班级的考勤规则
     * @param templateMap 规则模板映射，用于获取考勤规则的详细信息
     * @param clockInInfoDOList 考勤信息列表，用于存储生成的考勤信息
     */
    private void getDineAndLiveClockInfo(LocalDate nowDate, Map<Long, ClassRuleClockingInVO> classClockInMap, Map<Long, RuleTemplateDO> templateMap, List<ClockInInfoDO> clockInInfoDOList) {
        // 班级：包含是否开启到课、就餐、住宿考勤字段
        List<ClassManagementDO> classManagementDOS = classManagementMapper.selectList();
        Map<Long, ClassManagementDO> classManagementMap = classManagementDOS.stream()
                .collect(Collectors.toMap(ClassManagementDO::getId, cm -> cm, (existing, replacement) -> existing));
        List<Long> classIdList = classManagementDOS.stream().map(ClassManagementDO::getId).collect(Collectors.toList());
        List<TraineeDO> trainees = traineeService.lambdaQuery()
                .in(TraineeDO::getClassId, classIdList)
                .eq(TraineeDO::getStatus, TraineeStatusEnum.REPORTED.getStatus())
                .list();
        // 获取当天的全校考勤日历
        LambdaQueryWrapper<SchoolAccommodationAttendanceDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SchoolAccommodationAttendanceDO::getClockDate, nowDate);
        List<SchoolAccommodationAttendanceDO> attendanceList = schoolAccommodationAttendanceMapper.selectList(lambdaQueryWrapper);
        // 获取当天的班级考勤日历
        LambdaQueryWrapper<ClassClockCalendarDO> calendarDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        calendarDOLambdaQueryWrapper.eq(ClassClockCalendarDO::getClockDate, nowDate);
        List<ClassClockCalendarDO> calendarDOList = classClockCalendarMapper.selectList(calendarDOLambdaQueryWrapper);
        Map<Long, ClassClockCalendarDO> calendarMap = calendarDOList.stream()
                .collect(Collectors.toMap(ClassClockCalendarDO::getClassId, ccdo -> ccdo));
        if (attendanceList.isEmpty()) {
            throw new UnsupportedOperationException("当日全校考勤日历不存在！");
        }
        Map<Long, SchoolAccommodationAttendanceDO> attendanceMap = attendanceList.stream()
                .collect(Collectors.toMap(SchoolAccommodationAttendanceDO::getTenantId, attd -> attd,
                        // 处理键冲突的逻辑
                        (existingValue, newValue) -> existingValue
                ));
        for (TraineeDO traineeDO : trainees) {
            SchoolAccommodationAttendanceDO schoolAccommodationAttendanceDO = attendanceMap.get(traineeDO.getTenantId().longValue());
            //该租户下没有配置全校考勤日历
            if(schoolAccommodationAttendanceDO == null){
                continue;
            }
            // 当天的考勤开关
            Integer breakfast = schoolAccommodationAttendanceDO.getBreakfast();
            Integer lunch = schoolAccommodationAttendanceDO.getLunch();
            Integer dinner = schoolAccommodationAttendanceDO.getDinner();
            Integer putUp = schoolAccommodationAttendanceDO.getPutUp();
            ClassClockCalendarDO classClockCalendarDO = calendarMap.get(traineeDO.getClassId());
            if (classClockCalendarDO != null && classClockCalendarDO.getUpdateTime().isAfter(schoolAccommodationAttendanceDO.getUpdateTime())) {
                breakfast = classClockCalendarDO.getBreakfast();
                lunch = classClockCalendarDO.getLunch();
                dinner = classClockCalendarDO.getDinner();
                putUp = classClockCalendarDO.getPutUp();
            }
            ClassRuleClockingInVO classRuleClockingInVO = classClockInMap.get(traineeDO.getClassId());
            if(classRuleClockingInVO == null){
                continue;
            }
            ClassManagementDO classManagementDO = classManagementMap.get(traineeDO.getClassId());
            // 就餐考勤规则
            RuleTemplateDO ruleMealTemplateDO = templateMap.get(classRuleClockingInVO.getMealAttendance());
            // 住宿考勤规则
            RuleTemplateDO ruleCheckInTemplateDO = templateMap.get(classRuleClockingInVO.getCheckIn());
            if (ruleMealTemplateDO != null && IsCheckElseEnum.ON.getCode().equals(classManagementDO.getMealAttendance())) {
                // 早餐考勤
                if (!IsCheckEnum.ON.getCode().equals(breakfast)) {
                    ClockInInfoDO clockInInfoDO = new ClockInInfoDO();
                    clockInInfoDO.setClassId(traineeDO.getClassId());
                    clockInInfoDO.setTraineeId(traineeDO.getId());
                    clockInInfoDO.setTenantId(traineeDO.getTenantId().longValue());
                    clockInInfoDO.setType(TypeEnum.DINE.getCode());
                    clockInInfoDO.setMealPeriod(MealPeriodEnum.BREAKFAST.getCode());
                    clockInInfoDO.setDate(LocalDate.now());
                    // 考勤开始结束时间
                    clockInInfoDO.setCheckBeginTime(LocalDateTime.of(LocalDate.now(), ruleMealTemplateDO.getBreakfastStartTime()));
                    clockInInfoDO.setCheckEndTime(LocalDateTime.of(LocalDate.now(), ruleMealTemplateDO.getBreakfastEndTime()));
                    // 学员报到时间
                    clockInInfoDO.setReportTime(traineeDO.getReportTime());
                    clockInInfoDOList.add(clockInInfoDO);
                }
                // 午餐考勤
                if (!IsCheckEnum.ON.getCode().equals(lunch)) {
                    ClockInInfoDO clockInInfoDO = new ClockInInfoDO();
                    clockInInfoDO.setClassId(traineeDO.getClassId());
                    clockInInfoDO.setTraineeId(traineeDO.getId());
                    clockInInfoDO.setTenantId(traineeDO.getTenantId().longValue());
                    clockInInfoDO.setType(TypeEnum.DINE.getCode());
                    clockInInfoDO.setMealPeriod(MealPeriodEnum.LUNCH.getCode());
                    clockInInfoDO.setDate(LocalDate.now());
                    // 考勤开始结束时间
                    clockInInfoDO.setCheckBeginTime(LocalDateTime.of(LocalDate.now(), ruleMealTemplateDO.getLunchStartTime()));
                    clockInInfoDO.setCheckEndTime(LocalDateTime.of(LocalDate.now(), ruleMealTemplateDO.getLunchEndTime()));
                    // 学员报到时间
                    clockInInfoDO.setReportTime(traineeDO.getReportTime());
                    clockInInfoDOList.add(clockInInfoDO);
                }
                // 晚餐考勤
                if (!IsCheckEnum.ON.getCode().equals(dinner)) {
                    ClockInInfoDO clockInInfoDO = new ClockInInfoDO();
                    clockInInfoDO.setClassId(traineeDO.getClassId());
                    clockInInfoDO.setTraineeId(traineeDO.getId());
                    clockInInfoDO.setTenantId(traineeDO.getTenantId().longValue());
                    clockInInfoDO.setType(TypeEnum.DINE.getCode());
                    clockInInfoDO.setMealPeriod(MealPeriodEnum.DINNER.getCode());
                    clockInInfoDO.setDate(LocalDate.now());
                    // 考勤开始结束时间
                    clockInInfoDO.setCheckBeginTime(LocalDateTime.of(LocalDate.now(), ruleMealTemplateDO.getDinnerStartTime()));
                    clockInInfoDO.setCheckEndTime(LocalDateTime.of(LocalDate.now(), ruleMealTemplateDO.getDinnerEndTime()));
                    // 学员报到时间
                    clockInInfoDO.setReportTime(traineeDO.getReportTime());
                    clockInInfoDOList.add(clockInInfoDO);
                }
            }
            if (ruleCheckInTemplateDO != null && IsCheckElseEnum.ON.getCode().equals(classManagementDO.getCheckIn())) {
                // 住宿考勤
                if (!IsCheckEnum.ON.getCode().equals(putUp)) {
                    ClockInInfoDO clockInInfoDO = new ClockInInfoDO();
                    clockInInfoDO.setClassId(traineeDO.getClassId());
                    clockInInfoDO.setTraineeId(traineeDO.getId());
                    clockInInfoDO.setTenantId(traineeDO.getTenantId().longValue());
                    clockInInfoDO.setType(TypeEnum.LIVE.getCode());
                    clockInInfoDO.setDate(LocalDate.now());
                    // 考勤开始结束时间
                    clockInInfoDO.setCheckBeginTime(LocalDateTime.of(LocalDate.now(), ruleCheckInTemplateDO.getPutUpStartTime()));
                    clockInInfoDO.setCheckEndTime(LocalDateTime.of(LocalDate.now(), ruleCheckInTemplateDO.getPutUpEndTime()));
                    // 学员报到时间
                    clockInInfoDO.setReportTime(traineeDO.getReportTime());
                    clockInInfoDOList.add(clockInInfoDO);
                }
            }
        }
    }

    /**
     * 获取到课考勤信息
     *
     * @param nowDate 当前日期，用于查询当天的课程信息
     * @param classClockInMap 班级考勤规则映射，用于获取每个班级对应的考勤规则
     * @param templateMap 规则模板映射，用于获取考勤规则的详细信息
     * @param clockInInfoDOList 考勤信息列表，用于存储生成的考勤记录
     */
    private void getCourseClockInfo(LocalDate nowDate, Map<Long, ClassRuleClockingInVO> classClockInMap, Map<Long, RuleTemplateDO> templateMap, List<ClockInInfoDO> clockInInfoDOList) {
//        List<PlanDO> planList = planService.lambdaQuery().eq(PlanDO::getStatus, PlanStatusEnum.ON.getCode()).list();
//        List<Long> classIds = planList.stream().map(PlanDO::getClassId).collect(Collectors.toList());
//        List<Long> planIds = planList.stream().map(PlanDO::getId).collect(Collectors.toList());
        List<ClassCourseDO> classCourseList = classCourseService.lambdaQuery()
//                .in(ClassCourseDO::getPlanId, planIds)
//                .in(ClassCourseDO::getPlanId, planIds)
                .eq(ClassCourseDO::getDate, nowDate.toString())
                .eq(ClassCourseDO::getIsTemporary, false)
                .list();
        if(!classCourseList.isEmpty()){
            List<Long> classIds = classCourseList.stream().map(ClassCourseDO::getClassId).collect(Collectors.toList());
            List<TraineeDO> traineeList = traineeService.lambdaQuery()
                    .in(TraineeDO::getClassId, classIds)
                    .eq(TraineeDO::getStatus, TraineeStatusEnum.REPORTED.getStatus())
                    .list();
            List<Integer> classIdsAsIntegers = classIds.stream()
                    .map(Long::intValue)
                    .collect(Collectors.toList());
            // 仅用于到课考勤的班级生成记录
            List<ClassManagementDO> classManagementListOne = classManagementService.getClassManagementList(classIdsAsIntegers);
            for (ClassManagementDO classManagementDO : classManagementListOne) {
                // 本班级对应的到课考勤规则
                ClassRuleClockingInVO classRuleClockingInVO = classClockInMap.get(classManagementDO.getId());
                if (classRuleClockingInVO == null) {
                    continue;
                }
                RuleTemplateDO ruleTemplateDO = templateMap.get(classRuleClockingInVO.getAttendanceCheck());
                if (ruleTemplateDO != null) {
                    // 本班级未开启到课考勤
                    if (!IsCheckEnum.OFF.getCode().equals(classManagementDO.getAttendanceCheck())) {
                        continue;
                    }
                    // 本班级对应开启了考勤的课表课程
                    List<ClassCourseDO> classCourseListTemp = classCourseList.stream()
                            .filter(vo -> vo.getIsCheck() && classManagementDO.getId().equals(vo.getClassId()))
                            .collect(Collectors.toList());
                    if (classCourseListTemp.isEmpty()) {
                        continue;
                    }
                    // 本班级对应的所有学员
                    List<TraineeDO> traineeListTemp = traineeList.stream()
                            .filter(vo -> classManagementDO.getId().equals(vo.getClassId()))
                            .collect(Collectors.toList());
                    for (TraineeDO traineeDO : traineeListTemp) {
                        classCourseListTemp.forEach(classCourse -> {
                            ClockInInfoDO clockInInfoTemp = new ClockInInfoDO();
                            clockInInfoTemp.setClassId(classManagementDO.getId());
                            clockInInfoTemp.setTraineeId(traineeDO.getId());
                            clockInInfoTemp.setTenantId(traineeDO.getTenantId().longValue());
                            clockInInfoTemp.setClassCourseId(classCourse.getId());
                            clockInInfoTemp.setType(TypeEnum.COURSE.getCode());
                            clockInInfoTemp.setDate(LocalDate.now());
                            // 冗余字段 请假通过时根据这个字段判断学员请假状态
                            clockInInfoTemp.setCheckBeginTime(classCourse.getBeginTime());
                            clockInInfoTemp.setCheckEndTime(classCourse.getEndTime());
                            // 学员报到时间
                            clockInInfoTemp.setReportTime(traineeDO.getReportTime());
                            clockInInfoDOList.add(clockInInfoTemp);
                        });
                    }
                }
            }
        }
    }


    /**
     * 判断两个时间段是否重合，后修改为判断前者时间段是否被包含再后者时间段内，追加：只根据分判断，不看秒
     *
     * @param beginTime1 第一个时间段的开始时间
     * @param endTime1   第一个时间段的结束时间
     * @param beginTime2 第二个时间段的开始时间
     * @param endTime2   第二个时间段的结束时间
     * @return 如果时间重合返回true，否则返回false
     */
    @Override
    public boolean isTimeOverlap(
            LocalDateTime beginTime1, LocalDateTime endTime1,
            LocalDateTime beginTime2, LocalDateTime endTime2) {
        // 检查时间参数是否有效
        if (beginTime1 == null || endTime1 == null || beginTime2 == null || endTime2 == null) {
            throw new IllegalArgumentException("时间参数不能为空");
        }
        if (beginTime1.isAfter(endTime1) || beginTime2.isAfter(endTime2)) {
            throw new IllegalArgumentException("时间段的开始时间不应晚于结束时间");
        }
        // 将所有时间的秒和毫秒部分置为0,追加：最低只根据分为单位判断，不看秒
        beginTime1 = beginTime1.withSecond(0).withNano(0);
        endTime1 = endTime1.withSecond(0).withNano(0);
        beginTime2 = beginTime2.withSecond(0).withNano(0);
        endTime2 = endTime2.withSecond(0).withNano(0);
        // 判断前者时间段是否被包含再后者时间段内
        return (beginTime1.isBefore(beginTime2) || beginTime1.isEqual(beginTime2)) &&
                (endTime1.isAfter(endTime2) || endTime1.isEqual(endTime2));
    }

    /**
     * 学员状态分页
     *
     * @param pageReqVO 分页查询
     * @return 考勤签到分页
     */
    @Override
    public PageResult<ClockInInfoStudentStatusVO> getStudentClockingStatusPage(HttpServletRequest request,ClockInInfoPageReqVO pageReqVO) {

        // 判断该账户是否是学员
        Long userIds = SecurityFrameworkUtils.getLoginUserId();

        if (!traineeService.selectTraineeByUserId(request,userIds)) {
            throw exception(NO_PERMISSION_ERROR);
        }

        // 替换掉特殊符号  %  _
        if (StringUtils.isNotBlank(pageReqVO.getName())) {
            pageReqVO.setName(pageReqVO.getName().replaceAll("([%_])", "\\\\$1"));
        }

        Page buildPage = MyBatisUtils.buildPage(pageReqVO);

        List<ClockInInfoStudentStatusVO> clockingStatus = clockInInfoMapper.getStudentClockingStatusPage(buildPage, pageReqVO);

        PageResult<ClockInInfoStudentStatusVO> pageList = new PageResult<>(clockingStatus, buildPage.getTotal());

        return pageList;
    }

    /**
     * 获得列表, 用于 Excel 导出
     *
     * @param reqVO
     * @return
     */
    @Override
    public List<ClockInfoExcelVO> getClockInfoList(HttpServletRequest request,ClockInInfoExcelExportReqVO reqVO) {

        Integer ruleType = getRuleType();

        // 判断该账户是否是学员
        Long userIds = SecurityFrameworkUtils.getLoginUserId();

        if (!traineeService.selectTraineeByUserId(request,userIds)) {
            throw exception(NO_PERMISSION_ERROR);
        }

        // 替换掉特殊符号  %  _
        if (StringUtils.isNotBlank(reqVO.getName())) {
            reqVO.setName(reqVO.getName().replaceAll("([%_])", "\\\\$1"));
        }


        List<ClockInfoExcelVO> excelListInfo = new ArrayList<>();


        if (reqVO.getClockInType() == 0) {

            // 到课详情
            List<ClockInInfoReturnVO> attendanceCheck = clockInInfoMapper.selectClockInList(reqVO);

            // 插入比率
            for (ClockInInfoReturnVO vo : attendanceCheck) {

                ClockInfoExcelVO listVO = new ClockInfoExcelVO();

                listVO.setCourseDate(vo.getCourseDate());

                if (vo.getCoursePeriod().equals("0")) {
                    listVO.setType("上午到课");
                } else if (vo.getCoursePeriod().equals("1")) {
                    listVO.setType("下午到课");
                } else {
                    listVO.setType("晚上到课");
                }

                listVO.setCourseName(vo.getCourseName());
                listVO.setTraineeCount(vo.getTraineeCount());
                listVO.setAttendanceCount(vo.getAttendanceCount());
                listVO.setLatecomersCount(vo.getLatecomersCount());
                listVO.setVacateCount(vo.getVacateCount());
                listVO.setNoShowCount(vo.getNoShowCount());


                double traineeCount = vo.getTraineeCount();
                double vacateCount = vo.getVacateCount();
                double attendanceCount = vo.getAttendanceCount();

                double denominator;
                // 考勤率计算规则
                if (ClockRateRuleEnum.HAS_LEAVE.getCode().equals(ruleType)){
                    denominator = traineeCount - vacateCount;
                }else {
                    denominator = traineeCount;
                }

                double ratio = 0.0;

                if (denominator != 0) {
                    ratio = attendanceCount / denominator;
                }

                // 保留两位小数
                double ratioRounded = Math.round(ratio * 100.0) / 100.0;

                // 扩大100倍 转化成百分比
                double percentage = ratioRounded * 100;

                if (percentage > RatioValValueEnum.MAX_PERCENTAGE.getValue()){
                    percentage = RatioValValueEnum.MAX_PERCENTAGE.getValue();
                }

                DecimalFormat df = new DecimalFormat("0.00");

                String percentageFormatted = df.format(percentage);

                listVO.setCourseRatio(percentageFormatted);

                excelListInfo.add(listVO);
            }


        } else if (reqVO.getClockInType() == 1) {

            // 就餐详情
            List<ClockInInfoReturnVO> mealAttendance = clockInInfoMapper.selectListMealAttendance(reqVO);

            // 插入比率
            for (ClockInInfoReturnVO vo : mealAttendance) {

                ClockInfoExcelVO listVO = new ClockInfoExcelVO();

                listVO.setCourseDate(String.valueOf(vo.getClockDate()));

                if (vo.getMealPeriod() == 0) {
                    listVO.setType("早餐");
                } else if (vo.getMealPeriod() == 1) {
                    listVO.setType("中餐");
                } else {
                    listVO.setType("晚餐");
                }


                listVO.setTraineeCount(vo.getTraineeCount());
                listVO.setAttendanceCount(vo.getAttendanceCount());

                listVO.setVacateCount(vo.getVacateCount());
                listVO.setNoShowCount(vo.getNoShowCount());


                double traineeCount = vo.getTraineeCount();
                double vacateCount = vo.getVacateCount();
                double attendanceCount = vo.getAttendanceCount();

                double denominator;
                // 考勤率计算规则
                if (ClockRateRuleEnum.HAS_LEAVE.getCode().equals(ruleType)){
                    denominator = traineeCount - vacateCount;
                }else {
                    denominator = traineeCount;
                }

                double ratio = 0.0;

                if (denominator != 0) {
                    ratio = attendanceCount / denominator;
                }

                // 保留两位小数
                double ratioRounded = Math.round(ratio * 100.0) / 100.0;

                // 扩大100倍 转化成百分比
                double percentage = ratioRounded * 100;

                if (percentage > RatioValValueEnum.MAX_PERCENTAGE.getValue()){
                    percentage = RatioValValueEnum.MAX_PERCENTAGE.getValue();
                }

                DecimalFormat df = new DecimalFormat("0.00");

                String percentageFormatted = df.format(percentage);

                listVO.setMealRatio(percentageFormatted);


                // 插入日期
                Date clockDate = vo.getClockDate();

                // 使用 SimpleDateFormat 进行日期格式化
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String formattedDate = dateFormat.format(clockDate);

                // 设置到 listVO 中
                listVO.setCourseDate(formattedDate);


                excelListInfo.add(listVO);
            }


        } else {

            // 住宿详情
            List<ClockInInfoReturnVO> checkIn = clockInInfoMapper.selectListCheckIn(reqVO);

            // 插入比率
            for (ClockInInfoReturnVO vo : checkIn) {

                ClockInfoExcelVO listVO = new ClockInfoExcelVO();

                listVO.setCourseDate(String.valueOf(vo.getClockDate()));

                listVO.setType("住宿");


                listVO.setTraineeCount(vo.getTraineeCount());
                listVO.setAttendanceCount(vo.getAttendanceCount());

                listVO.setVacateCount(vo.getVacateCount());
                listVO.setNoShowCount(vo.getNoShowCount());


                double traineeCount = vo.getTraineeCount();
                double vacateCount = vo.getVacateCount();
                double attendanceCount = vo.getAttendanceCount();

                double denominator;
                // 考勤率计算规则
                if (ClockRateRuleEnum.HAS_LEAVE.getCode().equals(ruleType)){
                    denominator = traineeCount - vacateCount;
                }else {
                    denominator = traineeCount;
                }

                double ratio = 0.0;

                if (denominator != 0) {
                    ratio = attendanceCount / denominator;
                }

                // 保留两位小数
                double ratioRounded = Math.round(ratio * 100.0) / 100.0;

                // 扩大100倍 转化成百分比
                double percentage = ratioRounded * 100;

                if (percentage > RatioValValueEnum.MAX_PERCENTAGE.getValue()){
                    percentage = RatioValValueEnum.MAX_PERCENTAGE.getValue();
                }

                DecimalFormat df = new DecimalFormat("0.00");

                String percentageFormatted = df.format(percentage);

                listVO.setCheckInRatio(percentageFormatted);

                // 插入日期
                Date clockDate = vo.getClockDate();

                // 使用 SimpleDateFormat 进行日期格式化
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String formattedDate = dateFormat.format(clockDate);

                // 设置到 listVO 中
                listVO.setCourseDate(formattedDate);

                excelListInfo.add(listVO);

            }

        }


        return excelListInfo;
    }

    /**
     * 考勤三率
     *
     * @param reqVO 请求参数
     * @return 考勤三率
     */
    @Override
    public AttendanceRateRespVO getListForAttendanceThreeRate(AttendanceRateReqVO reqVO) {
        // 根据字典获取考勤率计算规则
        Integer ruleType = getRuleType();

        // 获取班级所有已报到、已结业学员信息
        List<AppTraineeGroupRespVO> traineeGroupRespVOList = traineeMapper.selectTraineeGroupListByClassIdAndStatus(reqVO.getClassId(),
                reqVO.getTraineeName(),
                Arrays.asList(TraineeStatusEnum.REPORTED.getStatus(), TraineeStatusEnum.GRADUATED.getStatus()));
        // 获取班级学员三类考勤信息
        List<TraineeAttendanceInfoDTO> attendanceInfoList = clockInInfoMapper.getTraineeAttendanceInfoList(reqVO, ruleType);
        // 获取班级学员到课考勤信息
        List<TraineeAttendanceInfoDTO> classAttendanceInfoList = attendanceInfoList.stream()
                .filter(o -> AttendanceTypeEnum.CLASS_ATTENDANCE.getStatus().equals(o.getType())).collect(Collectors.toList());
        Map<Long, TraineeAttendanceInfoDTO> traineeIdToClassAttendanceInfo = classAttendanceInfoList.stream()
                .collect(Collectors.toMap(TraineeAttendanceInfoDTO::getTraineeId, o -> o));
        // 获取班级学员就餐考勤信息
        List<TraineeAttendanceInfoDTO> mealAttendanceInfoList = attendanceInfoList.stream()
                .filter(o -> AttendanceTypeEnum.MEAL_ATTENDANCE.getStatus().equals(o.getType())).collect(Collectors.toList());
        Map<Long, TraineeAttendanceInfoDTO> traineeIdToMealAttendanceInfo = mealAttendanceInfoList.stream()
                .collect(Collectors.toMap(TraineeAttendanceInfoDTO::getTraineeId, o -> o));
        // 获取班级学员住宿考勤信息
        List<TraineeAttendanceInfoDTO> accommodationAttendanceInfoList = attendanceInfoList.stream()
                .filter(o -> AttendanceTypeEnum.ACCOMMODATION_ATTENDANCE.getStatus().equals(o.getType())).collect(Collectors.toList());
        Map<Long, TraineeAttendanceInfoDTO> traineeIdToAccommodationAttendanceInfo = accommodationAttendanceInfoList.stream()
                .collect(Collectors.toMap(TraineeAttendanceInfoDTO::getTraineeId, o -> o));
        // 获取平均到课率、平均就餐率、平均住宿率
        AttendanceRateRespVO attendanceRateRespVO = new AttendanceRateRespVO();
        List<AttendanceRateTraineeInfoVO> attendanceRateTraineeInfoVOList = packageToAttendanceRateTraineeInfoVOList(traineeGroupRespVOList);

        float classRateSum = 0;
        float mealRateSum = 0;
        float accommodationRateSum = 0;
        // 组装考勤信息
        for (AttendanceRateTraineeInfoVO attendanceRateTraineeInfoVO : attendanceRateTraineeInfoVOList) {
            Long traineeId = attendanceRateTraineeInfoVO.getTraineeId();
            // 到课考勤
            TraineeAttendanceInfoDTO traineeClassAttendanceInfo = traineeIdToClassAttendanceInfo.getOrDefault(traineeId, null);
            if (Objects.nonNull(traineeClassAttendanceInfo)) {
                attendanceRateTraineeInfoVO.setClassAttendanceExpected(traineeClassAttendanceInfo.getAttendanceExpected());
                attendanceRateTraineeInfoVO.setClassAttendanceActual(traineeClassAttendanceInfo.getAttendanceActual());
                attendanceRateTraineeInfoVO.setClassAttendanceLeave(traineeClassAttendanceInfo.getAttendanceLeave());
                traineeClassAttendanceInfo.setRate(RatioValValueEnum.getValidValueByDecimal(traineeClassAttendanceInfo.getRate()));
                // 保留小数点后两位
                attendanceRateTraineeInfoVO.setClassRate(formatRate(traineeClassAttendanceInfo.getRate()));
                classRateSum += traineeClassAttendanceInfo.getRate();
            } else {
                attendanceRateTraineeInfoVO.setClassAttendanceExpected(ZERO_INT);
                attendanceRateTraineeInfoVO.setClassAttendanceActual(ZERO_INT);
                attendanceRateTraineeInfoVO.setClassAttendanceLeave(ZERO_INT);
                attendanceRateTraineeInfoVO.setClassRate(ZERO_RATE_STRING);
            }
            // 就餐考勤
            TraineeAttendanceInfoDTO traineeMealAttendanceInfo = traineeIdToMealAttendanceInfo.getOrDefault(traineeId, null);
            if (Objects.nonNull(traineeMealAttendanceInfo)) {
                attendanceRateTraineeInfoVO.setMealAttendanceExpected(traineeMealAttendanceInfo.getAttendanceExpected());
                attendanceRateTraineeInfoVO.setMealAttendanceActual(traineeMealAttendanceInfo.getAttendanceActual());
                attendanceRateTraineeInfoVO.setMealAttendanceLeave(traineeMealAttendanceInfo.getAttendanceLeave());
                traineeMealAttendanceInfo.setRate(RatioValValueEnum.getValidValueByDecimal(traineeMealAttendanceInfo.getRate()));
                attendanceRateTraineeInfoVO.setMealRate(formatRate(traineeMealAttendanceInfo.getRate()));
                mealRateSum += traineeMealAttendanceInfo.getRate();
            } else {
                attendanceRateTraineeInfoVO.setMealAttendanceExpected(ZERO_INT);
                attendanceRateTraineeInfoVO.setMealAttendanceActual(ZERO_INT);
                attendanceRateTraineeInfoVO.setMealAttendanceLeave(ZERO_INT);
                attendanceRateTraineeInfoVO.setMealRate(ZERO_RATE_STRING);
            }
            // 住宿考勤
            TraineeAttendanceInfoDTO traineeAccommodationAttendanceInfo = traineeIdToAccommodationAttendanceInfo.getOrDefault(traineeId, null);
            if (Objects.nonNull(traineeAccommodationAttendanceInfo)) {
                attendanceRateTraineeInfoVO.setAccommodationAttendanceExpected(traineeAccommodationAttendanceInfo.getAttendanceExpected());
                attendanceRateTraineeInfoVO.setAccommodationAttendanceActual(traineeAccommodationAttendanceInfo.getAttendanceActual());
                attendanceRateTraineeInfoVO.setAccommodationAttendanceLeave(traineeAccommodationAttendanceInfo.getAttendanceLeave());
                traineeAccommodationAttendanceInfo.setRate(RatioValValueEnum.getValidValueByDecimal(traineeAccommodationAttendanceInfo.getRate()));
                attendanceRateTraineeInfoVO.setAccommodationRate(formatRate(traineeAccommodationAttendanceInfo.getRate()));
                accommodationRateSum += traineeAccommodationAttendanceInfo.getRate();
            } else {
                attendanceRateTraineeInfoVO.setAccommodationAttendanceExpected(ZERO_INT);
                attendanceRateTraineeInfoVO.setAccommodationAttendanceActual(ZERO_INT);
                attendanceRateTraineeInfoVO.setAccommodationAttendanceLeave(ZERO_INT);
                attendanceRateTraineeInfoVO.setAccommodationRate(ZERO_RATE_STRING);
            }
        }
        boolean empty = traineeGroupRespVOList.isEmpty();
        attendanceRateRespVO.setAverageClassRate(empty ? ZERO_RATE_STRING : formatRate(
                RatioValValueEnum.getValidValueByPercentage(classRateSum / traineeGroupRespVOList.size())));
        attendanceRateRespVO.setAverageMealRate(empty ? ZERO_RATE_STRING : formatRate(
                RatioValValueEnum.getValidValueByPercentage(mealRateSum / traineeGroupRespVOList.size())));
        attendanceRateRespVO.setAverageAccommodationRate(empty ? ZERO_RATE_STRING : formatRate(
                RatioValValueEnum.getValidValueByPercentage(accommodationRateSum / traineeGroupRespVOList.size())));
        attendanceRateRespVO.setTraineeInfoList(attendanceRateTraineeInfoVOList);
        return attendanceRateRespVO;
    }

    /**
     * 百分比转换规则
     * （到课率、就餐率、住宿率、平均到课率、平均就餐率、平均住宿率）需要转换为百分比格式显示；
     * 如果转换后是整数百分比（如100%、75%、20%），则不显示小数部分；
     * 如果转换后有小数（如75.60%、98.75%），则保留小数点后两位。
     * @param rate 百分比值
     * @return 格式化后的百分比字符串
     */
    public static String formatRate(Float rate) {
        if (rate == null) {
            return "";
        }

        // 创建一个 DecimalFormat 对象，设置格式为百分比并保留两位小数
        DecimalFormat decimalFormat = new DecimalFormat("0.00%");
        String formattedRate = decimalFormat.format(rate);

        // 检查是否是整数百分比
        if (formattedRate.contains(".")) {
            String[] parts = formattedRate.split("\\.");
            if (parts.length > 1 && parts[1].equals("00%")) {
                // 如果小数部分是 .00%，则去掉小数部分
                formattedRate = parts[0] + "%";
            }
        }

        return formattedRate;
    }

    /**
     * 业中调用获取考勤三率
     *
     * @param reqVO 请求对象，包含请求参数
     * @return AttendanceRateForYZRespVO 响应数据
     */
    @Override
    public AttendanceRateForBusinessCenterRespVO getListForAttendanceThreeRateForBusinessCenter(AttendanceRateForBusinessCenterReqVO reqVO) {
        AttendanceRateForBusinessCenterRespVO attendanceRateForBusinessCenterRespVO = new AttendanceRateForBusinessCenterRespVO();
        Integer ruleType = getRuleType();
        // 年度转为开始时间-结束时间
        if (Objects.nonNull(reqVO.getYear())) {
            reqVO.setStartTime(LocalDate.of(reqVO.getYear(), Month.JANUARY, 1));
            reqVO.setEndTime(LocalDate.of(reqVO.getYear(), Month.DECEMBER, 31));
        }

        // 当前时间
        LocalDateTime currentTime = LocalDateTime.now();
        List<AttendanceDetailsVO> list = clockInInfoMapper.selectAttendanceRateForYZ(reqVO, currentTime);

        // 设置平均到率
        list.forEach(item -> {
            try {
                int attendanceExpected = item.getAttendanceExpected();
                // 考勤率计算规则 实到/（应到-请假）
                if (ClockRateRuleEnum.HAS_LEAVE.getCode().equals(ruleType)){
                    attendanceExpected = attendanceExpected - item.getAttendanceLeave();
                }

                if (item.getAttendanceExpected() == 0) {
                    item.setAverageRate("0.0");
                    return;
                }

                BigDecimal result = BigDecimal.valueOf(item.getAttendanceActual() * 100.0 / attendanceExpected);
                BigDecimal roundedResult = result.setScale(1, RoundingMode.HALF_UP); // 保留一位小数，四舍五入
                item.setAverageRate(roundedResult.toString());
            }catch (Exception e){
                log.error("仪表盘-考勤三率-计算考勤率出错", e);
                item.setAverageRate("0.0");
            }
        });
        attendanceRateForBusinessCenterRespVO.setAttendanceDetailsVOList(list);
        return attendanceRateForBusinessCenterRespVO;
    }

    /**
     * 获取租户是否开启考勤保护
     * @return 考勤保护是否开启 true 开启，false 未开启
     */
    @Override
    public Boolean isEnableAttendanceProtection(Long tenantId) {
        if (Objects.isNull(tenantId)) {
            tenantId = getTenantId();
        }

        // 租户为空时，默认不开启考勤保护
        if (Objects.isNull(tenantId)){
            return false;
        }
        return clockInInfoMapper.isEnableAttendanceProtection(tenantId);
    }

    private Integer getRuleType() {
        Long tenantId = getTenantId();
        Integer ruleType = defaultRuleType;
        List<DictDataRespDTO> dictDataRespDTOS = dictDataApi.getByDictTypes(Collections.singletonList(rateRuleDictType)).getCheckedData();
        // 获取字典默认的考勤三率计算规则
        DictDataRespDTO defaultDTO = dictDataRespDTOS.stream().filter(dictDataRespDTO -> defaultRuleDictLabel.equals(dictDataRespDTO.getLabel())).findFirst().orElse(null);

        if (Objects.nonNull(defaultDTO)) {
            ruleType = Integer.valueOf(defaultDTO.getValue());
        }

        try{
            DictDataRespDTO checkedData = dictDataRespDTOS.stream().filter(dto-> dto.getLabel().equals(String.valueOf(tenantId)))
                    .findFirst().orElse(null);
            log.info("获取租户{}考勤三率规则成功'{}'",tenantId, checkedData);
            if (checkedData != null){
                ruleType = Integer.valueOf(checkedData.getValue());
                String desc = ClockRateRuleEnum.getDescByCode(ruleType);
                if (desc == null){
                    log.info("字典获取租户{}，考勤三率规则值错误'{}'，使用默认规则", tenantId, ruleType);
                    ruleType = defaultRuleType;
                }
            }else {
                log.info("字典获取租户{}，考勤三率规则为空，使用默认规则：{}", tenantId, ruleType);
            }
        }catch (Exception e){
            log.info("字典获取考勤三率规则失败'{}'，使用默认规则", e.getMessage());
        }
        log.info("租户id = '{}' 考勤三率计算规则：{}",tenantId, ruleType);
        return ruleType;
    }

    /**
     * 获取学生上课未到、请假详情列表
     *
     * @param reqVO 请求参数
     * @return 详情列表
     */
    @Override
    public List<AttendanceTraineeClassInfoRespVO> getClassTraineeNotArrivedAndLeaveInfo(AttendanceNotArrivedAndLeaveInfoReqVO reqVO) {
        List<AttendanceTraineeClassInfoRespVO> list = clockInInfoMapper.getClassTraineeNotArrivedAndLeaveInfo(reqVO);
        list.forEach(item -> {
            item.setClassStartTimeStr(DateUtils.format(item.getClassStartTime(), DateUtils.FORMAT_HOUR_MINUTE));
            item.setClassEndTimeStr(DateUtils.format(item.getClassEndTime(), DateUtils.FORMAT_HOUR_MINUTE));
        });
        return list;
    }

    /**
     * 获取学生就餐未到、请假详情列表
     *
     * @param reqVO 请求参数
     * @return 详情列表
     */
    @Override
    public List<AttendanceTraineeMealInfoRespVO> getMealTraineeNotArrivedAndLeaveInfo(AttendanceNotArrivedAndLeaveInfoReqVO reqVO) {
        return clockInInfoMapper.getMealTraineeNotArrivedAndLeaveInfo(reqVO);
    }

    /**
     * 获取学生住宿未到、请假详情列表
     *
     * @param reqVO 请求参数
     * @return 详情列表
     */
    @Override
    public List<AttendanceTraineeAccommodationInfoRespVO> getAccommodationTraineeNotArrivedAndLeaveInfo(AttendanceNotArrivedAndLeaveInfoReqVO reqVO) {
        return clockInInfoMapper.getAccommodationTraineeNotArrivedAndLeaveInfo(reqVO);
    }

    /**
     * 获取班级某天考勤详情
     *
     * @param dateStr 日期
     * @param classId 班级id
     * @return 班级某天考勤详情
     */
    @Override
    public List<AppAttendanceDetailsRespVO> getDetailsForAppTeacher(String dateStr, Long classId) {
        // date 日期解析
        LocalDate date = DateUtils.parseLocalDate(dateStr, DateUtils.FORMAT_YEAR_MONTH_DAY);
        List<AppAttendanceDetailsRespVO> detailsRespList = new ArrayList<>();
        // 获取班级某天的到课考勤详情
        List<AppAttendanceDetailsRespVO> classDetails = clockInInfoMapper.getClassDetails(date, classId);
        if (CollUtil.isNotEmpty(classDetails)) {
            AttendanceCheckTime attendanceCheckTime = clockInInfoMapper.getAttendanceCheckTime(classId);
            DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("HH:mm:ss");
            classDetails.forEach(item -> {
                if (Objects.nonNull(attendanceCheckTime) && Objects.nonNull(attendanceCheckTime.getBeforeClassMinute())
                        && Objects.nonNull(attendanceCheckTime.getAfterClassMinute())) {
                    String checkBeginTime = item.getClassStartTime().minusMinutes(attendanceCheckTime.getBeforeClassMinute()).format(formatter1);
                    String checkEndTime = item.getClassStartTime().plusMinutes(attendanceCheckTime.getAfterClassMinute()).format(formatter2);
                    item.setCheckTime(checkBeginTime + "~" + checkEndTime);
                } else {
                    item.setCheckTime(item.getClassStartTime().format(formatter1));
                }
            });
        }
        // 回显查询参数
        setQueryParams(classDetails, date, classId, AttendanceTypeEnum.CLASS_ATTENDANCE);
        // 获取班级某天的就餐考勤详情
        List<AppAttendanceDetailsRespVO> mealDetails = clockInInfoMapper.getMealDetails(date, classId);
        // 回显查询参数
        setQueryParams(mealDetails, date, classId, AttendanceTypeEnum.MEAL_ATTENDANCE);
        // 获取班级某天的住宿考勤详情
        List<AppAttendanceDetailsRespVO> accommodationDetails = clockInInfoMapper.getAccommodationDetails(date, classId);
        // 回显查询参数
        setQueryParams(accommodationDetails, date, classId, AttendanceTypeEnum.ACCOMMODATION_ATTENDANCE);

        // 组装一天考勤项 早餐 上午课程 午餐 下午课程 晚餐
        // 早餐
        List<AppAttendanceDetailsRespVO> mealBreakfastDetails = mealDetails.stream().filter(item -> ClassDayPeriodEnum.AM.getPeriod()
                .equals(item.getMealPeriod())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(mealBreakfastDetails)) {
            detailsRespList.addAll(mealBreakfastDetails);
        }
        // 上午课程
        List<AppAttendanceDetailsRespVO> classAmDetails = classDetails.stream().filter(item -> ClassDayPeriodEnum.AM.getPeriod()
                .equals(item.getCoursePeriod())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(classAmDetails)) {
            detailsRespList.addAll(classAmDetails);
        }
        // 午餐
        List<AppAttendanceDetailsRespVO> mealLunchDetails = mealDetails.stream().filter(item -> ClassDayPeriodEnum.PM.getPeriod()
                .equals(item.getMealPeriod())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(mealLunchDetails)) {
            detailsRespList.addAll(mealLunchDetails);
        }
        // 下午课程
        List<AppAttendanceDetailsRespVO> classPmDetails = classDetails.stream().filter(item -> ClassDayPeriodEnum.PM.getPeriod()
                .equals(item.getCoursePeriod())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(classPmDetails)) {
            detailsRespList.addAll(classPmDetails);
        }
        // 晚上课程
        List<AppAttendanceDetailsRespVO> classEvDetails = classDetails.stream().filter(item -> ClassDayPeriodEnum.NIGHT.getPeriod()
                .equals(item.getCoursePeriod())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(classEvDetails)) {
            detailsRespList.addAll(classEvDetails);
        }
        // 晚餐
        List<AppAttendanceDetailsRespVO> mealDinnerDetails = mealDetails.stream().filter(item -> ClassDayPeriodEnum.NIGHT.getPeriod()
                .equals(item.getMealPeriod())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(mealDinnerDetails)) {
            detailsRespList.addAll(mealDinnerDetails);
        }
        // 住宿
        if (CollUtil.isNotEmpty(accommodationDetails)) {
            detailsRespList.addAll(accommodationDetails);
        }
        return detailsRespList;
    }


    /**
     * 获取一月班级考勤异常的日期列表
     *
     * @param date    年月 yy-mm
     * @param classId 班级id
     * @return 班级考勤异常的日期列表
     */
    @Override
    public List<String> getAbnormalAttendanceDateList(String date, Long classId) {
        // date 是年月 2022-01 因此加上月第一天
        String monthStartStr = date + "-01";
        LocalDate monthStart = DateUtils.parseLocalDate(monthStartStr, DateUtils.FORMAT_YEAR_MONTH_DAY);
        // 下个月第一天
        LocalDate monthEnd = monthStart.plusMonths(1);
        List<LocalDate> dateList = clockInInfoMapper.getAbnormalAttendanceDateList(monthStart, monthEnd, classId);
        return dateList.stream().map(dateTime ->
                DateUtils.format(dateTime, DateUtils.FORMAT_YEAR_MONTH_DAY)).collect(Collectors.toList());
    }

    /**
     * 获取考勤项学员考勤具体情况列表
     *
     * @param reqVO 请求参数
     * @return 详情列表
     */
    @Override
    public List<AppTraineeAttendanceDetailsRespVO> getTraineeAttendanceDetails(AppTraineeAttendanceDetailsReqVO reqVO) {
        return clockInInfoMapper.getTraineeAttendanceDetails(reqVO);
    }

    private List<AttendanceRateTraineeInfoVO> packageToAttendanceRateTraineeInfoVOList(List<AppTraineeGroupRespVO> traineeGroupRespVOList) {
        return traineeGroupRespVOList.stream().map(traineeGroupRespVO -> {
            AttendanceRateTraineeInfoVO attendanceRateTraineeInfoVO = new AttendanceRateTraineeInfoVO();
            attendanceRateTraineeInfoVO.setTraineeId(traineeGroupRespVO.getId());
            attendanceRateTraineeInfoVO.setGroupName(Objects.isNull(traineeGroupRespVO.getGroupName()) ? UN_GROUPED_NAME : traineeGroupRespVO.getGroupName());
            attendanceRateTraineeInfoVO.setTraineeName(traineeGroupRespVO.getName());
            attendanceRateTraineeInfoVO.setSex(traineeGroupRespVO.getSex());
            attendanceRateTraineeInfoVO.setPosition(traineeGroupRespVO.getPosition());
            attendanceRateTraineeInfoVO.setClassCommitteeId(traineeGroupRespVO.getClassCommitteeId());
            attendanceRateTraineeInfoVO.setClassCommitteeName(traineeGroupRespVO.getClassCommitteeName());
            return attendanceRateTraineeInfoVO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取学员考勤信息
     * 根据学员类型和考勤类型，获取相应的考勤信息和规则
     *
     * @param type 考勤类型，用于区分课程考勤、就餐考勤和住宿考勤
     * @return 返回包含考勤信息的响应对象
     */
    @Override
    public AppClockInInfoRespVO getAppClockInfo(Integer type) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        AppClockInInfoRespVO appClockInInfoRespVO = new AppClockInInfoRespVO();
        TraineeDO traineeDO = traineeLoginTypeRedisDAO.get(userId);
        if (traineeDO == null){
            traineeDO = traineeMapper.selectByUserId(userId);
            if (traineeDO == null){
                throw exception(NO_PERMISSION_ERROR);
            }
            traineeLoginTypeRedisDAO.set(userId, traineeDO);
            log.info("redis缓存中没有获取到userid = {}, 数据库获取到学员信息：{}", userId, traineeDO);
        }else {
            log.info("缓存获取到userid = {}, 学员信息：{}", userId, traineeDO);
        }

        // 获取当前学员所在班级，用于获取班级的考勤规则
        Long classId = traineeDO.getClassId();
        Long traineeId = traineeDO.getId();
        if (TypeEnum.COURSE.getCode().equals(type)) {
            // 获取到课考勤信息
            appClockInInfoRespVO = getCourseClockInInfoRespVO(classId, traineeId);
        } else if (TypeEnum.DINE.getCode().equals(type)) {
            // 获取就餐考勤信息
            getDineClockInInfoRespVO(classId, traineeId, appClockInInfoRespVO);
        } else if (TypeEnum.LIVE.getCode().equals(type)) {
            //获取住宿考勤信息
            getLiveClockInInfoRespVO(classId, appClockInInfoRespVO, traineeId);
        } else {
            throw exception(CLOCK_TYPE_NOT_EXISTS);
        }
        return appClockInInfoRespVO;
    }

    /**
     * 获取住宿打卡信息响应对象
     *
     * @param classId 课程ID
     * @param appClockInInfoRespVO App打卡信息响应对象，用于存储打卡相关信息
     * @param traineeId 学员ID
     */
    private void getLiveClockInInfoRespVO(Long classId, AppClockInInfoRespVO appClockInInfoRespVO, Long traineeId) {
        // 获取住宿考勤规则
        ClassRuleClockingInVO classClockInByAttendanceCheck = classManagementMapper.getClassRuleClockingInVObyTypeClass(TypeEnum.LIVE, classId);

        // 住宿考勤
        // 获取住宿考勤规则
        if (classClockInByAttendanceCheck == null) {
            throw exception(CLOCK_INFO_LIVE_NOT_EXISTS);
        }
        Long attendanceCheckId = classClockInByAttendanceCheck.getCheckIn();
        RuleTemplateRespVO ruleTemplate = ruleTemplateService.getRuleTemplate(attendanceCheckId);
        if (ruleTemplate == null) {
            throw exception(CLOCK_INFO_LIVE_NOT_EXISTS);
        }
        // 考勤经纬度及范围
        List<RuleTemplateLocationDO> ruleTemplateLocationDOS = ruleTemplateMapper.getRuleTemplateLocationDOlist(ruleTemplate.getId());

        if (ruleTemplateLocationDOS.isEmpty()) {
            throw exception(CLOCK_INFO_LIVE_NOT_EXISTS);
        } else {
            String areaPoint = ruleTemplateLocationDOS.stream()
                    .map(location -> "[" + location.getLongitude() + "," + location.getLatitude() + "]")
                    .collect(Collectors.joining(","));
            String range = ruleTemplateLocationDOS.stream()
                    .map(location -> "[" + location.getRange() + "]")
                    .collect(Collectors.joining(","));
            String locationName = ruleTemplateLocationDOS.stream()
                    .map(location -> "[" + location.getLocationName() + "]")
                    .collect(Collectors.joining(","));
//                // 判断是否处于住宿打卡时间内
//                if (isWithinInterval(LocalTime.now(), ruleTemplate.getPutUpStartTime(), ruleTemplate.getPutUpEndTime())) {
//                    appClockInInfoRespVO.setCheckBeginTime(LocalDateTime.of(LocalDate.now(), ruleTemplate.getPutUpStartTime()));
//                    appClockInInfoRespVO.setCheckEndTime(LocalDateTime.of(LocalDate.now(), ruleTemplate.getPutUpEndTime()));
//                } else {
//                    throw exception(CLOCK_INFO_LIVE_NOT_EXISTS);
//                }
            appClockInInfoRespVO.setCheckBeginTime(LocalDateTime.of(LocalDate.now(), ruleTemplate.getPutUpStartTime()));
            appClockInInfoRespVO.setCheckEndTime(LocalDateTime.of(LocalDate.now(), ruleTemplate.getPutUpEndTime()));
            // 设置打卡id
            List<ClockInInfoDO> checkList = this.lambdaQuery()
                    .eq(ClockInInfoDO::getClassId, classId)
                    .eq(ClockInInfoDO::getTraineeId, traineeId)
                    .eq(ClockInInfoDO::getType, TypeEnum.LIVE.getCode())
                    .eq(ClockInInfoDO::getDate, LocalDate.now())
                    .list();
            if (checkList.isEmpty()) {
                throw exception(CLOCK_INFO_LIVE_NOT_EXISTS);
            } else {
                //只有处于住宿打卡时间范围内时，才设置打卡id
                if (isWithinInterval(LocalTime.now(), ruleTemplate.getPutUpStartTime(), ruleTemplate.getPutUpEndTime())) {
                    appClockInInfoRespVO.setCheckId(checkList.get(0).getId());
                }
                appClockInInfoRespVO.setStatus(checkList.get(0).getTraineeStatus().toString());
            }
            appClockInInfoRespVO.setAreaPoint(areaPoint);
            appClockInInfoRespVO.setRange(range);
            appClockInInfoRespVO.setHostelName(locationName);
        }
    }

    /**
     * 获取就餐考勤信息响应对象
     *
     * @param classId 班级ID
     * @param traineeId 学员ID
     * @param appClockInInfoRespVO 就餐考勤信息响应对象，用于存储查询到的考勤信息
     */
    private void getDineClockInInfoRespVO(Long classId, Long traineeId, AppClockInInfoRespVO appClockInInfoRespVO) {
        // 获取就餐考勤规则
        ClassRuleClockingInVO classClockInByAttendanceCheck = classManagementMapper.getClassRuleClockingInVObyTypeClass(TypeEnum.DINE, classId);

        // 就餐考勤
        // 获取就餐考勤规则
        if (classClockInByAttendanceCheck == null) {
            throw exception(CLOCK_INFO_DINE_NOT_EXISTS);
        }
        Long attendanceCheckId = classClockInByAttendanceCheck.getMealAttendance();
        RuleTemplateRespVO ruleTemplate = ruleTemplateService.getRuleTemplate(attendanceCheckId);
        if (ruleTemplate == null) {
            throw exception(CLOCK_INFO_DINE_NOT_EXISTS);
        }
        // 考勤经纬度及范围
        List<RuleTemplateLocationDO> ruleTemplateLocationDOS = ruleTemplateMapper.getRuleTemplateLocationDOlist(ruleTemplate.getId());
        if (ruleTemplateLocationDOS.isEmpty()) {
            throw exception(CLOCK_INFO_DINE_NOT_EXISTS);
        } else {
            String areaPoint = ruleTemplateLocationDOS.stream()
                    .map(location -> "[" + location.getLongitude() + "," + location.getLatitude() + "]")
                    .collect(Collectors.joining(","));
            String range = ruleTemplateLocationDOS.stream()
                    .map(location -> "[" + location.getRange() + "]")
                    .collect(Collectors.joining(","));
            String locationName = ruleTemplateLocationDOS.stream()
                    .map(location -> "[" + location.getLocationName() + "]")
                    .collect(Collectors.joining(","));
            // 判断是否处于早餐、午餐或晚餐的打卡时间内
            Integer mealPeriod = null;
            if (isWithinInterval(LocalTime.now(), ruleTemplate.getBreakfastStartTime(), ruleTemplate.getBreakfastEndTime())) {
                mealPeriod = MealPeriodEnum.BREAKFAST.getCode();
            } else if (isWithinInterval(LocalTime.now(), ruleTemplate.getLunchStartTime(), ruleTemplate.getLunchEndTime())) {
                mealPeriod = MealPeriodEnum.LUNCH.getCode();
            } else if (isWithinInterval(LocalTime.now(), ruleTemplate.getDinnerStartTime(), ruleTemplate.getDinnerEndTime())) {
                mealPeriod = MealPeriodEnum.DINNER.getCode();
            } else {
                // 修改为不处在时间段内也返回
//                    throw exception(CLOCK_INFO_DINE_NOT_EXISTS);
            }
            // 设置打卡id
            if (mealPeriod != null) {
                List<ClockInInfoDO> checkList = this.lambdaQuery()
                        .eq(ClockInInfoDO::getClassId, classId)
                        .eq(ClockInInfoDO::getTraineeId, traineeId)
                        .eq(ClockInInfoDO::getMealPeriod, mealPeriod)
                        .eq(ClockInInfoDO::getDate, LocalDate.now())
                        .list();
                if (checkList.isEmpty()) {
                    throw exception(CLOCK_INFO_DINE_NOT_EXISTS);
                } else {
                    appClockInInfoRespVO.setCheckId(checkList.get(0).getId());
                    appClockInInfoRespVO.setStatus(checkList.get(0).getTraineeStatus().toString());
                }
            }
            appClockInInfoRespVO.setAreaPoint(areaPoint);
            appClockInInfoRespVO.setRange(range);
            appClockInInfoRespVO.setCanteenName(locationName);
            appClockInInfoRespVO.setBreakfastTimeRange(ruleTemplate.getBreakfastStartTime().toString() + " - " + ruleTemplate.getBreakfastEndTime());
            appClockInInfoRespVO.setLunchTimeRange(ruleTemplate.getLunchStartTime().toString() + " - " + ruleTemplate.getLunchEndTime());
            appClockInInfoRespVO.setDinnerTimeRange(ruleTemplate.getDinnerStartTime().toString() + " - " + ruleTemplate.getDinnerEndTime());
        }
    }

    /**
     * 获取课程考勤信息响应对象
     *
     * @param classId 课程ID
     * @param traineeId 学员ID
     * @return AppClockInInfoRespVO对象，包含课程考勤信息
     */
    private AppClockInInfoRespVO getCourseClockInInfoRespVO(Long classId, Long traineeId) {
        AppClockInInfoRespVO appClockInInfoRespVO;
        // 获取到课考勤规则
        ClassRuleClockingInVO classClockInByAttendanceCheck = classManagementMapper.getClassRuleClockingInVObyTypeClass(TypeEnum.COURSE, classId);

        // 到课考勤
        // 返回 考勤开始时间 到 课程结束时间 之间的课程信息，仅展示一条
        // 获取当前、当天登录学员我的课表
        // 获取当天的开始时间（即午夜00:00:00）
        LocalTime startOfDay = LocalTime.MIN;
        LocalDateTime startOfDayDateTime = LocalDateTime.of(LocalDate.now(), startOfDay);
        String startOfDayStr = startOfDayDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
        // 获取当天的结束时间（即次日午夜00:00:00，即当天的23:59:59.999）
        // 减去1秒以得到当天的最后一刻
        LocalTime endOfDay = LocalTime.MAX.minusSeconds(1);
        LocalDateTime endOfDayDateTime = LocalDateTime.of(LocalDate.now(), endOfDay);
        String endOfDayStr = endOfDayDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
        MyClassScheduleParamsVO myClassScheduleParamsVO = new MyClassScheduleParamsVO();
        myClassScheduleParamsVO.setStartTime(startOfDayStr);
        myClassScheduleParamsVO.setEndTime(endOfDayStr);
        List<ScheduleChartVO> myClassSchedule = classCourseService.getMyClassSchedule(myClassScheduleParamsVO);
        // 获取当天课程
        ScheduleChartVO scheduleChartVO = myClassSchedule.stream()
                .filter(vo -> LocalDate.now().toString().equals(vo.getDate()))
                .findFirst()
                .orElse(null);
        if (scheduleChartVO == null) {
            throw exception(CLOCK_INFO_APP_NOT_EXISTS);
        }
        List<MyClassScheduleVO> myClassScheduleVOList = scheduleChartVO.getSchedules();
        // 本版本筛选不考虑选修课打卡
        myClassScheduleVOList = myClassScheduleVOList.stream()
//                    .filter(vo -> !CoursesTypeEnum.OPTIONAL_COURSE.getType().equals(vo.getCoursesType().intValue()))
                .collect(Collectors.toList());
        List<AppClockInInfoRespVO> appClockInInfoRespVOList = ClockInInfoConvert.INSTANCE.convertToAppRespVoList(myClassScheduleVOList);
        // 获取到课考勤规则
        if (classClockInByAttendanceCheck == null) {
            throw exception(CLOCK_INFO_APP_NOT_EXISTS);
        }
        Long attendanceCheckId = classClockInByAttendanceCheck.getAttendanceCheck();
        RuleTemplateRespVO ruleTemplate = ruleTemplateService.getRuleTemplate(attendanceCheckId);
        // 根据考勤规则中的上课前打卡时间、上课后打卡时间，得出并设置返回类中的打卡时间范围
        if (ruleTemplate == null) {
            throw exception(CLOCK_INFO_APP_NOT_EXISTS);
        }
        Integer beforeTime = ruleTemplate.getBeforeClassTime();
        Integer afterTime = ruleTemplate.getAfterClassTime();
        appClockInInfoRespVOList.forEach(vo -> {
            vo.setCheckBeginTime(vo.getBeginTime().minusMinutes(beforeTime.longValue()));
            vo.setCheckEndTime(vo.getBeginTime().plusMinutes(afterTime.longValue()));
        });
        List<AppClockInInfoRespVO> filteredList = appClockInInfoRespVOList.stream()
                .filter(vo -> !LocalDateTime.now().isBefore(vo.getCheckBeginTime()) && !LocalDateTime.now().isAfter(vo.getEndTime()))
                .collect(Collectors.toList());
        // 正常业务流程下同一时间只有一节课可以打卡,且一直可以显示到该堂课结束的那一节课（打卡开始时间和课程结束时间之间的那一堂课）
        if (filteredList.isEmpty()) {
            throw exception(CLOCK_INFO_APP_NOT_EXISTS);
        } else {
            appClockInInfoRespVO = filteredList.get(0);
        }
        List<ClockInInfoDO> checkList = this.lambdaQuery()
                .eq(ClockInInfoDO::getClassId, classId)
                .eq(ClockInInfoDO::getTraineeId, traineeId)
                .eq(ClockInInfoDO::getClassCourseId, appClockInInfoRespVO.getClassCourseId())
                .list();
        if (checkList.isEmpty()) {
            throw exception(CLOCK_INFO_APP_NOT_EXISTS);
        } else {
            appClockInInfoRespVO.setCheckId(checkList.get(0).getId());
            appClockInInfoRespVO.setStatus(checkList.get(0).getTraineeStatus().toString());
        }
        // 考勤经纬度及范围
        List<RuleTemplateLocationDO> ruleTemplateLocationDOS = ruleTemplateMapper.getRuleTemplateLocationDOlist(ruleTemplate.getId());
        if (ruleTemplateLocationDOS.isEmpty()) {
            throw exception(CLOCK_INFO_APP_NOT_EXISTS);
        } else {
            String areaPoint = ruleTemplateLocationDOS.stream()
                    .map(location -> "[" + location.getLongitude() + "," + location.getLatitude() + "]")
                    .collect(Collectors.joining(","));
            String range = ruleTemplateLocationDOS.stream()
                    .map(location -> "[" + location.getRange() + "]")
                    .collect(Collectors.joining(","));

            appClockInInfoRespVO.setAreaPoint(areaPoint);
            appClockInInfoRespVO.setRange(range);
        }
        return appClockInInfoRespVO;
    }


    /**
     * 获取当前登录学员的班级id
     */
    private Long getTraineeClassId() {
        // 判断登录用户是否在学员表中存在
        Long userIds = SecurityFrameworkUtils.getLoginUserId();
        // 获取学员id
        Long eduTraineeId = classCourseMapper.getEduTraineeId(userIds);
        if (eduTraineeId == null || eduTraineeId == 0) {
            throw exception(NO_PERMISSION_ERROR);
        }
        // 获取学员班级id
        return classCourseMapper.getEduTraineeClassId(userIds);
    }

    /**
     * 获取当前登录学员Id
     */
    private Long getTraineeId() {
        // 判断登录用户是否在学员表中存在
        Long userIds = SecurityFrameworkUtils.getLoginUserId();
        // 获取学员id
        Long eduTraineeId = classCourseMapper.getEduTraineeId(userIds);
        if (eduTraineeId == null || eduTraineeId == 0) {
            throw exception(NO_PERMISSION_ERROR);
        }
        return eduTraineeId;
    }

    private static void setQueryParams(List<AppAttendanceDetailsRespVO> accommodationDetails,
                                       LocalDate date,
                                       Long classId,
                                       AttendanceTypeEnum accommodationAttendance) {
        for (AppAttendanceDetailsRespVO accommodationDetail : accommodationDetails) {
            accommodationDetail.setDate(date);
            accommodationDetail.setClassId(classId);
            accommodationDetail.setType(accommodationAttendance.getStatus());
        }
    }

    /**
     * 判断就餐时间是否处于打卡时间内
     */
    private boolean isWithinInterval(LocalTime time, LocalTime start, LocalTime end) {
        return !time.isBefore(start) && !time.isAfter(end);
    }


    /**
     * 学员移动端-大课考勤、点名签到
     *
     * @param type
     * @return 签到信息
     */
    @Override
    public AppClockInInfoRespVO getRollCallInfo(Integer type) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        AppClockInInfoRespVO appClockInInfoRespVO = new AppClockInInfoRespVO();
        TraineeDO traineeDO = traineeLoginTypeRedisDAO.get(userId);
        if (traineeDO == null){
            traineeDO = traineeMapper.selectByUserId(userId);
            if (traineeDO == null){
                throw exception(NO_PERMISSION_ERROR);
            }
            traineeLoginTypeRedisDAO.set(userId, traineeDO);
            log.info("redis缓存中没有获取到userid = {}, 数据库获取到学员信息：{}", userId, traineeDO);
        }else {
            log.info("缓存获取到userid = {}, 学员信息：{}", userId, traineeDO);
        }
        // 获取当前学员所在班级，用于获取班级的考勤规则
        Long classId = getTraineeClassId();
        Long traineeId = getTraineeId();
        // 获取考勤规则
        LocalDateTime nowDateTime = LocalDateTime.now();
        List<RollcallSignInDO> lectureRuleList = rollcallSignInMapper.selectListByCurrentTimeRule(classId,
                type, nowDateTime);
        if (SignInTypeEnum.LECTURE_ATTENDANCE.getStatus().equals(type)) {
            //获取大课考勤信息
            getLectureAttendance(lectureRuleList, appClockInInfoRespVO, traineeId);
        } else if (SignInTypeEnum.ROLL_CALL_SIGN_IN.getStatus().equals(type)) {
            //获取点名签到信息
            getRollCallSignIn(lectureRuleList, appClockInInfoRespVO, traineeId);
        } else {
            throw exception(CLOCK_TYPE_NOT_EXISTS);
        }
        return appClockInInfoRespVO;
    }

    /**
     * 根据点名签到规则列表和学员ID获取签到信息
     * 此方法主要用于处理学员的打卡签到信息，根据点名签到规则列表中的第一条规则（假定为当前有效的规则），
     * 设置App钟签到信息响应对象的相关属性，并检查学员是否已经有签到记录
     *
     * @param lectureRuleList 点名签到规则列表，包含多个RollcallSignInDO对象
     * @param appClockInInfoRespVO App钟签到信息响应对象，用于返回签到相关信息
     * @param traineeId 学员ID，用于标识特定的学员
     * @throws Exception 如果点名签到规则列表为空或学员没有签到记录，则抛出异常
     */
    private void getRollCallSignIn(List<RollcallSignInDO> lectureRuleList, AppClockInInfoRespVO appClockInInfoRespVO, Long traineeId) {
        if (lectureRuleList.isEmpty()) {
            throw exception(ROLL_SIGN_CHECK_NOT_EXISTS);
        }
        // 正常业务流程下同一时间只有一节课可以打卡,且一直可以显示到该堂课结束的那一节课（打卡开始时间和课程结束时间之间的那一堂课）
        RollcallSignInDO rollcallSignInDO = lectureRuleList.get(0);
//            // 教师手动结束考勤
//            if (rollcallSignInDO.getEnded()) {
//                throw exception(ROLL_SIGN_CHECK_NOT_EXISTS);
//            }
        appClockInInfoRespVO.setSignInId(rollcallSignInDO.getId());
        appClockInInfoRespVO.setCheckBeginTime(rollcallSignInDO.getCheckStartTime());
        appClockInInfoRespVO.setCheckEndTime(rollcallSignInDO.getCheckEndTime());
        appClockInInfoRespVO.setTitle(rollcallSignInDO.getTitle());
        appClockInInfoRespVO.setClassId(rollcallSignInDO.getClassId());
        appClockInInfoRespVO.setTraineeId(traineeId);
        // 打卡位置
        appClockInInfoRespVO.setLocation(rollcallSignInDO.getAddress());
        // 考勤经纬度及范围
        appClockInInfoRespVO.setAreaPoint(rollcallSignInDO.getLongitude() + "," + rollcallSignInDO.getLatitude());
        appClockInInfoRespVO.setRange(rollcallSignInDO.getRadius().toString());
        List<RollcallRecordDO> list = rollcallRecordService.lambdaQuery().eq(RollcallRecordDO::getRollcallId, rollcallSignInDO.getId()).eq(RollcallRecordDO::getTraineeId, traineeId).list();
        if (list.isEmpty()) {
            throw exception(ROLL_SIGN_CHECK_NOT_EXISTS);
        }
        appClockInInfoRespVO.setCheckId(list.get(0).getId().intValue());
        appClockInInfoRespVO.setStatus(list.get(0).getStatus().toString());
    }

    /**
     * 获取大课考勤信息
     * 此方法用于根据大课考勤规则列表和学员ID，获取大课考勤的考勤信息，并将其填充到AppClockInInfoRespVO对象中
     *
     * @param lectureRuleList 大课考勤规则列表，包含大课考勤的签到规则信息
     * @param appClockInInfoRespVO App考勤信息响应VO，用于存储大课考勤相关信息
     * @param traineeId 学员ID，用于标识特定的学员
     */
    private void getLectureAttendance(List<RollcallSignInDO> lectureRuleList, AppClockInInfoRespVO appClockInInfoRespVO, Long traineeId) {
        if (lectureRuleList.isEmpty()) {
            throw exception(ROLL_SIGN_LECTURE_NOT_EXISTS);
        }
        // 正常业务流程下同一时间只有一节课可以打卡,且一直可以显示到该堂课结束的那一节课（打卡开始时间和课程结束时间之间的那一堂课）
        RollcallSignInDO rollcallSignInDO = lectureRuleList.get(0);
        ClassCourseDO classCourseDO = classCourseService.getById(rollcallSignInDO.getClassCourseId());

        // 若该堂课已调课，则无法打卡大课考勤
//            if (classCourseDO == null || classCourseDO.getIsChange()) {
        if (classCourseDO == null || classCourseDO.getCourseId() == null) {
            throw exception(ROLL_SIGN_LECTURE_NOT_EXISTS);
        }

//            // 教师手动结束考勤
//            if (rollcallSignInDO.getEnded()) {
//                throw exception(ROLL_SIGN_LECTURE_NOT_EXISTS);
//            }
        ClassCourseRespVO respVO = classCourseMapper.selectRespVOById(classCourseDO.getId());
        if (respVO != null) {
            appClockInInfoRespVO.setTeacherName(respVO.getTeacherName());
        }
        ClassroomLibraryDO classRoomDO = classroomLibraryService.getById(classCourseDO.getClassroomId());
        if (classRoomDO != null) {
            appClockInInfoRespVO.setClassName(classRoomDO.getClassName());
        }
        CoursesDO coursesDO = coursesMapper.selectById(classCourseDO.getCourseId());
        if (coursesDO != null) {
            appClockInInfoRespVO.setCourseName(coursesDO.getName());
        }
        appClockInInfoRespVO.setSignInId(rollcallSignInDO.getId());
        appClockInInfoRespVO.setCheckBeginTime(rollcallSignInDO.getCheckStartTime());
        appClockInInfoRespVO.setCheckEndTime(rollcallSignInDO.getCheckEndTime());
        appClockInInfoRespVO.setLocation(rollcallSignInDO.getAddress());
        appClockInInfoRespVO.setBeginTime(classCourseDO.getBeginTime());
        appClockInInfoRespVO.setEndTime(classCourseDO.getEndTime());
        // 设置课程id
        appClockInInfoRespVO.setClassCourseId(classCourseDO.getId());
        appClockInInfoRespVO.setTraineeId(traineeId);
        appClockInInfoRespVO.setClassId(classCourseDO.getClassId());
        // 考勤经纬度及范围
        appClockInInfoRespVO.setAreaPoint(rollcallSignInDO.getLongitude() + "," + rollcallSignInDO.getLatitude());
        appClockInInfoRespVO.setRange(rollcallSignInDO.getRadius().toString());
        List<ClockInInfoDO> list = this.lambdaQuery()
                .eq(ClockInInfoDO::getTraineeId, traineeId)
                .eq(ClockInInfoDO::getClassCourseId, classCourseDO.getId())
                .list();
        if (list.isEmpty()) {
            throw exception(ROLL_SIGN_LECTURE_NOT_EXISTS);
        }
        appClockInInfoRespVO.setCheckId(list.get(0).getId());
        appClockInInfoRespVO.setStatus(list.get(0).getTraineeStatus().toString());
    }

    @Override
    public Boolean checkInById(Integer id, Boolean isLate) {
        //传入需要打卡的签到表主键id来更新相应的打卡记录
        //校验对应的打卡记录是否真实存在，不存在则将异常抛出
//        validateClockInInfoExists(id);
        //已打卡的记录无需重复打卡
        ClockInInfoDO clockInInfoDOPre = validateClockInInfoExists(id);
        if(ClockStatusEnum.DONE.getCode().equals(clockInInfoDOPre.getTraineeStatus())){
            throw exception(HAVE_DONE);
        }
        //校验当前是否处于可打卡状态
        AppClockInInfoRespVO appClockInfo = getAppClockInfo(clockInInfoDOPre.getType());
        if(id == null && appClockInfo.getCheckId() != null){
            id = appClockInfo.getCheckId();
        }
        //更新待打卡记录
        ClockInInfoDO clockInInfoDO = new ClockInInfoDO();
        clockInInfoDO.setId(id);
        //设置更新的打卡记录状态为已打卡
        clockInInfoDO.setTraineeStatus(ClockStatusEnum.DONE.getCode());
        //到课考勤：修改为由后端判定是否迟到，当前时间晚于考勤结束时间，则为迟到
        if (appClockInfo.getCheckEndTime() != null && LocalDateTime.now().isAfter(appClockInfo.getCheckEndTime()) && TypeEnum.COURSE.getCode().equals(clockInInfoDOPre.getType())) {
            isLate = true;
        }
        //根据前端传来的是否迟到来判断是否要设置打卡的更新时间段为是否迟到
        if (isLate) {
            clockInInfoDO.setTraineeStatus(ClockStatusEnum.LATE.getCode());
        }
        //设置打卡时间为当下时间
        clockInInfoDO.setClockingTime(LocalDateTime.now());
        //更新打卡记录
        int result = clockInInfoMapper.updateById(clockInInfoDO);
        //到课考勤更新同午别下的打卡状态为一致
        //判断当前租户是否已开启午别打卡规则，且打卡类型为到课考勤
        if (tenantApi.getTenantCheckRuleById(getTenantId()).getCheckedData() && TypeEnum.COURSE.getCode().equals(clockInInfoDOPre.getType())) {
            LambdaUpdateWrapper<ClockInInfoDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ClockInInfoDO::getTraineeId, clockInInfoDOPre.getTraineeId())
                    .eq(ClockInInfoDO::getPeriod, clockInInfoDOPre.getPeriod())
                    .eq(ClockInInfoDO::getDate, clockInInfoDOPre.getDate())
                    //不更新已请假的考勤记录
                    .ne(ClockInInfoDO::getTraineeStatus, ClockStatusEnum.LEAVE.getCode())
                    .set(ClockInInfoDO::getTraineeStatus, clockInInfoDO.getTraineeStatus());
            clockInInfoMapper.update(null, updateWrapper);
        }
        return result > 0;
    }

    @Override
    public Boolean checkInByIdAndMobile(Integer id){
        //传入需要打卡的签到表主键id来更新相应的打卡记录
        //校验对应的打卡记录是否真实存在，不存在则将异常抛出
        validateClockInInfoExists(id);
        //已打卡的记录无需重复打卡
        ClockInInfoDO clockInInfoDOPre = clockInInfoMapper.selectById(id);
        if(ClockStatusEnum.DONE.getCode().equals(clockInInfoDOPre.getTraineeStatus())){
            throw exception(HAVE_DONE);
        }
        ClockInInfoDO clockInInfoDO = new ClockInInfoDO();
        clockInInfoDO.setId(id);
        //设置更新的打卡记录状态为已打卡
        clockInInfoDO.setTraineeStatus(ClockStatusEnum.DONE.getCode());
        //后端判断是否迟到的情况
        ClockInInfoDO infoDO = this.lambdaQuery().eq(ClockInInfoDO::getId, clockInInfoDO.getId()).one();
        if(infoDO.getClassCourseId() != null) {
            //校验当前是否可打卡
            AppClockInInfoRespVO appClockInfo = getAppClockInfo(TypeEnum.COURSE.getCode());
            //判断是否迟到的情况
            if (LocalDateTime.now().isAfter(appClockInfo.getCheckEndTime())) {
                clockInInfoDO.setTraineeStatus(ClockStatusEnum.LATE.getCode());
            }
        }
        //设置打卡时间为当下时间
        clockInInfoDO.setClockingTime(LocalDateTime.now());
        return this.updateById(clockInInfoDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Async
    public void deleteClockInInfoByTraineeId(Long traineeId) {
        LambdaQueryWrapper<ClockInInfoDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ClockInInfoDO::getTraineeId, traineeId);
        List<ClockInInfoDO> list = this.list(wrapper);
        if (CollUtil.isEmpty(list)){
            return;
        }

        this.removeBatchByIds(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Async
    public void deleteClockInInfoByTraineeIds(List<Long> ids) {
        LambdaQueryWrapper<ClockInInfoDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ClockInInfoDO::getTraineeId, ids);
        List<ClockInInfoDO> list = this.list(wrapper);
        if (CollUtil.isEmpty(list)){
            return;
        }

        this.removeBatchByIds(list);
    }

    @Override
    public Boolean checkInLectureById(Integer id, Boolean isLate) {
        validateClockInInfoExists(id);
        // 校验老师是否已手动结束点到：调用原接口，若抛出无需签到，则已手动结束
        AppClockInInfoRespVO clockInInfoRespVO = getRollCallInfo(SignInTypeEnum.LECTURE_ATTENDANCE.getStatus());
        ClockInInfoDO clockInInfoDO = new ClockInInfoDO();
        clockInInfoDO.setId(id);
        clockInInfoDO.setTraineeStatus(ClockStatusEnum.DONE.getCode());
        // if (isLate) {
        //     clockInInfoDO.setTraineeStatus(ClockStatusEnum.LATE.getCode());
        // }
        LocalDateTime currentTime = LocalDateTime.now();
        clockInInfoDO.setClockingTime(currentTime);
        // 标记为大课考勤签到
        clockInInfoDO.setLargeAttendanceId(clockInInfoRespVO.getSignInId());

        if (currentTime.isAfter(clockInInfoRespVO.getEndTime())) {
            log.info("大课考勤签到时间{}在课程结束{}之后，实时补发问卷",currentTime,
                    clockInInfoRespVO.getEndTime());
            // 签到时间在课程结束之后，则签到后实时补发问卷
            // 注：课程结束后定时下发问卷有一个窗口时间期
            // 这边先于定时器下发问卷  定时器会进行判重-1.3.0同步修改
            EvaluationDistributeVO evaluationDistributeVO = new EvaluationDistributeVO();
            evaluationDistributeVO.setClassCourseId(clockInInfoRespVO.getClassCourseId());
            evaluationDistributeVO.setStudentId(clockInInfoRespVO.getTraineeId());
            // 补发问卷 判重1.3.0同步修改
            try {
                evaluationDetailService.distributeEvaluation(evaluationDistributeVO);
                log.info("traineeId = '{}', classCourseId = '{}',补发问卷成功",
                        evaluationDistributeVO.getStudentId(),
                        evaluationDistributeVO.getClassCourseId());
            }catch (Exception e){
                log.info("签到成功！补发问卷异常，如果定时器已经下发过问卷则无影响", e);
            }
        }
        return this.updateById(clockInInfoDO);
    }

    @Override
    public Boolean checkInRollCallById(Integer id) {
        validateRollcallRecordExists(id);
        // 校验老师是否已手动结束点到：调用原接口，若抛出无需签到，则已手动结束
        getRollCallInfo(SignInTypeEnum.ROLL_CALL_SIGN_IN.getStatus());
        RollcallRecordDO rollcallRecordDO = new RollcallRecordDO();
        rollcallRecordDO.setId(id.longValue());
        rollcallRecordDO.setStatus(ClockStatusEnum.DONE.getCode());
        return rollcallRecordService.updateById(rollcallRecordDO);
    }


    @Override
    public List<MyErrorClockInInfoRespVO> getMyErrorClockInInfoList(MyErrorClockInInfoReqVO reqVO) {

        Long userId = SecurityFrameworkUtils.getLoginUserId();
        // 根据userId获取对应的学员id
        TraineeDO traineeDO = traineeService.getTraineeByUserId(userId);

        if (traineeDO == null) {
            throw exception(NO_PERMISSION_ERROR);
        }

        if (reqVO.getTraineeId() == null){
            reqVO.setTraineeId(traineeDO.getId());
        }

        List<CourseVO> clockInfoDOList = clockInInfoMapper.selectErrorClock(reqVO);

        List<MyErrorClockInInfoRespVO> list = new ArrayList<>();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        for (CourseVO courseVO : clockInfoDOList) {
            MyErrorClockInInfoRespVO respVO = new MyErrorClockInInfoRespVO();
            respVO.setDate(courseVO.getCreateTime().format(formatter));
            Map<String, String> map = new HashMap<>();
            // 前端要求这样传
            map.put("v1", "异常的一天");
            respVO.setData(map);
            list.add(respVO);
        }

        return list;
    }

    /**
     * 更新学员打卡状态
     *
     * @param recordId 学员打卡记录id
     * @param status   打卡状态
     */
    @Override
    public void updateTraineeAttendanceStatus(Long recordId, Integer status) {
        String descByStatus = UpdateAttendanceStatusEnum.getDescByStatus(status);
        if (StringUtils.isBlank(descByStatus)) {
            throw exception(ATTENDANCE_UPDATE_STATUS_NOT_EXISTS);
        }
        ClockInInfoDO clockInInfoDO = validateClockInInfoExists(recordId);
        if (clockInInfoDO.getType().equals(0)) {
            // 专题课且未签调整成正常/迟到时下发问卷
            if (clockInInfoDO.getTraineeStatus().equals(0) && (status.equals(1) || status.equals(2))) {
                LocalDateTime endTime = classCourseMapper.selectById(clockInInfoDO.getClassCourseId()).getEndTime();
                // 课程结束后的补卡才会补发问卷
                if (endTime.isBefore(LocalDateTime.now())) {
                    try {
                        evaluationDetailService.distributeEvaluation(new EvaluationDistributeVO()
                                .setStudentId(clockInInfoDO.getTraineeId()).setClassCourseId(clockInInfoDO.getClassCourseId()));
                    }catch (Exception e){
                        log.info("补发问卷异常", e);
                    }

                }
            }

            // 专题课且正常/迟到调整为未签、请假时回收问卷
            if ((clockInInfoDO.getTraineeStatus().equals(1) || clockInInfoDO.getTraineeStatus().equals(2))) {
                if (status > UpdateAttendanceStatusEnum.LATE.getStatus()) {
                    evaluationDetailService.revokeQuestionnaire(clockInInfoDO.getTraineeId(), clockInInfoDO.getClassCourseId());
                }
            }
        }
        if (status > UpdateAttendanceStatusEnum.LATE.getStatus()) {
            // // 1.3.0 就餐、住宿无请假概念
            // // 2025-02-19恢复住宿、就餐请假概念
            // if (AttendanceTypeEnum.ACCOMMODATION_ATTENDANCE.getStatus().equals(clockInInfoDO.getType())
            // || AttendanceTypeEnum.MEAL_ATTENDANCE.getStatus().equals(clockInInfoDO.getType())){
            //     throw exception(CLOCK_LEAVE_NOT_SUPPORT);
            // }
            // 请假
            clockInInfoDO.setTraineeStatus(AttendanceStatusEnum.LEAVE.getStatus());
            // 多条件判断请假类型
            if (UpdateAttendanceStatusEnum.PERSONAL_LEAVE.getStatus().equals(status)) {
                clockInInfoDO.setLeaveType(TraineeLeaveType.BUSINESS.getCode());
            } else if (UpdateAttendanceStatusEnum.SICK_LEAVE.getStatus().equals(status)) {
                clockInInfoDO.setLeaveType(TraineeLeaveType.SICK.getCode());
            } else if (UpdateAttendanceStatusEnum.FIVE_MEETING_LEAVE.getStatus().equals(status)) {
                clockInInfoDO.setLeaveType(TraineeLeaveType.FIVE_CANS.getCode());
            }
        } else {
            clockInInfoDO.setTraineeStatus(status);
            clockInInfoDO.setLeaveType(null);
        }
        // 更新
        clockInInfoMapper.updateById(clockInInfoDO);
    }

    @Override
    public void batchUpdateTraineeAttendanceStatus(List<Long> recordIds, Integer status) {
        if (CollUtil.isEmpty(recordIds)) {
            return;
        }

        String descByStatus = UpdateAttendanceStatusEnum.getDescByStatus(status);
        if (StringUtils.isBlank(descByStatus)) {
            throw exception(ATTENDANCE_UPDATE_STATUS_NOT_EXISTS);
        }

        // 获取所有考勤记录
        List<ClockInInfoDO> clockInInfoDOList = clockInInfoMapper.selectBatchIds(recordIds);
        if (CollUtil.isEmpty(clockInInfoDOList)) {
            return;
        }

        // 需要更新的记录列表
        List<ClockInInfoDO> updateList = new ArrayList<>(clockInInfoDOList.size());

        for (ClockInInfoDO clockInInfoDO : clockInInfoDOList) {
            if (clockInInfoDO.getType().equals(0)) {
                // 专题课且未签调整成正常/迟到时下发问卷
                if (clockInInfoDO.getTraineeStatus().equals(0) && (status.equals(1) || status.equals(2))) {
                    LocalDateTime endTime = classCourseMapper.selectById(clockInInfoDO.getClassCourseId()).getEndTime();
                    // 课程结束后的补卡才会补发问卷
                    if (endTime.isBefore(LocalDateTime.now())) {
                        try {
                            evaluationDetailService.distributeEvaluation(new EvaluationDistributeVO()
                                    .setStudentId(clockInInfoDO.getTraineeId())
                                    .setClassCourseId(clockInInfoDO.getClassCourseId()));
                        } catch (Exception e) {
                            log.info("补发问卷异常", e);
                        }
                    }
                }

                // 专题课且正常/迟到调整为未签、请假时回收问卷
                if ((clockInInfoDO.getTraineeStatus().equals(1) || clockInInfoDO.getTraineeStatus().equals(2))) {
                    if (status > UpdateAttendanceStatusEnum.LATE.getStatus()) {
                        evaluationDetailService.revokeQuestionnaire(clockInInfoDO.getTraineeId(),
                                clockInInfoDO.getClassCourseId());
                    }
                }
            }

            // 设置状态和请假类型
            if (status > UpdateAttendanceStatusEnum.LATE.getStatus()) {
                // 请假
                clockInInfoDO.setTraineeStatus(AttendanceStatusEnum.LEAVE.getStatus());
                // 多条件判断请假类型
                if (UpdateAttendanceStatusEnum.PERSONAL_LEAVE.getStatus().equals(status)) {
                    clockInInfoDO.setLeaveType(TraineeLeaveType.BUSINESS.getCode());
                } else if (UpdateAttendanceStatusEnum.SICK_LEAVE.getStatus().equals(status)) {
                    clockInInfoDO.setLeaveType(TraineeLeaveType.SICK.getCode());
                } else if (UpdateAttendanceStatusEnum.FIVE_MEETING_LEAVE.getStatus().equals(status)) {
                    clockInInfoDO.setLeaveType(TraineeLeaveType.FIVE_CANS.getCode());
                }
            } else {
                clockInInfoDO.setTraineeStatus(status);
                clockInInfoDO.setLeaveType(null);
            }

            updateList.add(clockInInfoDO);
        }

        // 批量更新
        if (!updateList.isEmpty()) {
            clockInInfoMapper.updateBatch(updateList);
        }
    }

    /**
     * 批量学员补卡
     *
     * @param reqVO 补卡参数
     */
    @Override
    public void batchUpdateToCheckIn(AppBatchUpdateToCheckInReqVO reqVO) {

        if (CollUtil.isEmpty(reqVO.getRecordIdList())) {
            return;
        }
        List<ClockInInfoDO> clockInInfoDOList = clockInInfoMapper.selectBatchIds(reqVO.getRecordIdList());
        Integer leaveType = null;
        Integer traineeStatus;
        if (reqVO.getStatus() > UpdateAttendanceStatusEnum.LATE.getStatus()) {
            // 请假
            traineeStatus = AttendanceStatusEnum.LEAVE.getStatus();
            // 多条件判断请假类型
            if (UpdateAttendanceStatusEnum.PERSONAL_LEAVE.getStatus().equals(reqVO.getStatus())) {
                leaveType = TraineeLeaveType.BUSINESS.getCode();
            } else if (UpdateAttendanceStatusEnum.SICK_LEAVE.getStatus().equals(reqVO.getStatus())) {
                leaveType = TraineeLeaveType.SICK.getCode();
            } else if (UpdateAttendanceStatusEnum.FIVE_MEETING_LEAVE.getStatus().equals(reqVO.getStatus())) {
                leaveType = TraineeLeaveType.FIVE_CANS.getCode();
            }
        } else {
            traineeStatus = reqVO.getStatus();
        }

        for (ClockInInfoDO o : clockInInfoDOList) {
            if (o.getType().equals(0)) {
                // 专题课且未签调整成正常/迟到时下发问卷
                if (o.getTraineeStatus().equals(0) && (reqVO.getStatus().equals(1) || reqVO.getStatus().equals(2))) {
                    LocalDateTime endTime = classCourseMapper.selectById(o.getClassCourseId()).getEndTime();
                    // 课程结束后的补卡才会补发问卷
                    if (endTime.isBefore(LocalDateTime.now())) {
                        try {
                            evaluationDetailService.distributeEvaluation(new EvaluationDistributeVO()
                                    .setStudentId(o.getTraineeId()).setClassCourseId(o.getClassCourseId()));
                        }catch (Exception e){
                            log.info("补发问卷失败", e);
                        }
                    }
                }

                // 专题课且正常/迟到调整为未签、请假时回收问卷
                if ((o.getTraineeStatus().equals(1) || o.getTraineeStatus().equals(2))) {
                    if (reqVO.getStatus() > UpdateAttendanceStatusEnum.LATE.getStatus()) {
                        evaluationDetailService.revokeQuestionnaire(o.getTraineeId(), o.getClassCourseId());
                    }
                }
            }
            o.setTraineeStatus(traineeStatus);
            o.setLeaveType(leaveType);
            o.setUpdater(null);
            o.setUpdateTime(null);
        }
        if (CollUtil.isNotEmpty(clockInInfoDOList)) {
            clockInInfoMapper.updateBatch(clockInInfoDOList);
        }
    }

    @Override
    public void modifyLeaveClockIn(TraineeLeaveDO leaveInfo) {
        // 就餐、住宿 请假时间段完全覆盖打卡时间为请假
        // 获取请假时间段完全覆盖的学员打卡记录
        List<ClockInInfoDO> clockInInfoList = clockInInfoMapper.selectListByTraineeIdAndLeaveTime(leaveInfo.getTraineeId(),
                leaveInfo.getStartTime(), leaveInfo.getEndTime());
        log.info("学员请假通过！学员请假信息：{}", leaveInfo);
        log.info("学员请假时间段对应打卡记录：{}", clockInInfoList);
        clockInInfoList.forEach(o -> {
            o.setTraineeStatus(AttendanceStatusEnum.LEAVE.getStatus());
            o.setLeaveType(leaveInfo.getLeaveType());
        });
        if (!clockInInfoList.isEmpty()) {
            clockInInfoMapper.updateBatch(clockInInfoList);
        }
    }

    @Override
    public void modifyLeaveCancel(TraineeLeaveDO leaveInfo) {
        // 就餐、住宿 请假时间段完全覆盖打卡时间为请假
        // 获取请假时间段完全覆盖的学员打卡记录
        LocalDateTime now = LocalDateTime.now();

        List<ClockInInfoDO> clockInInfoList = clockInInfoMapper.selectListByTraineeIdAndLeaveTime(leaveInfo.getTraineeId(),
                leaveInfo.getStartTime(), leaveInfo.getEndTime());
        log.info("学员撤回请假通过！学员请假信息：{}", leaveInfo);
        log.info("学员请假时间段对应打卡记录：{}", clockInInfoList);
        if (!clockInInfoList.isEmpty()) {
            clockInInfoList.forEach(o -> {
                o.setTraineeStatus(AttendanceStatusEnum.NOT_ARRIVE.getStatus());
                o.setLeaveType(null);
            });
            clockInInfoMapper.updateBatch(clockInInfoList);
        }
    }

    @Override
    public List<TraineeAttendanceInfoDTO> getLateInfo(AttendanceRateReqVO reqVO) {
        Integer ruleType = getRuleType();
        // 获取班级学员三类考勤信息
        List<TraineeAttendanceInfoDTO> attendanceInfoList = clockInInfoMapper.getLateInfo(reqVO,ruleType);
        return attendanceInfoList;
    }

    @Override
    public PageResult<ClassAttendanceDetailRespVO> getClassAttendanceDetailsPage(
            ClassAttendanceDetailPageReqVO pageVO) {
        // 构建分页对象
        Page<ClassAttendanceDetailRespVO> page = MyBatisUtils.buildPage(pageVO);

        try {
            // 查询班级学员考勤详情列表
            List<ClassAttendanceDetailRespVO> list = clockInInfoMapper.getClassAttendanceDetailsPage(page, pageVO);

            if (list == null || list.isEmpty()) {
                log.info("班级学员考勤详情查询结果为空，classId={}, date={}, type={}",
                        pageVO.getClassId(), pageVO.getAttendanceDate(), pageVO.getType());
                return new PageResult<>(new ArrayList<>(), 0L);
            }

            // 设置状态描述
            list.forEach(item -> {
                if (item.getStatus() != null) {
                    switch (item.getStatus()) {
                        case 0:
                            item.setStatusDesc("未到");
                            break;
                        case 1:
                            item.setStatusDesc("正常");
                            break;
                        case 2:
                            item.setStatusDesc("迟到");
                            break;
                        case 3:
                            item.setStatusDesc("事假");
                            break;
                        case 4:
                            item.setStatusDesc("病假");
                            break;
                        case 5:
                            item.setStatusDesc("五会假");
                            break;
                        default:
                            item.setStatusDesc("未知");
                    }
                }
                // 设置序号
                item.setSerialNumber(list.indexOf(item) + 1 + (pageVO.getPageNo() - 1) * pageVO.getPageSize());
            });

            // 返回分页结果
            return new PageResult<>(list, page.getTotal());
        } catch (Exception e) {
            log.error("查询班级学员考勤详情分页发生异常", e);
            return new PageResult<>(new ArrayList<>(), 0L);
        }
    }
}
